# ===================================================================
# Blog Workflow LangGraph - Pytest Configuration
# ===================================================================

[tool:pytest]
# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Output and reporting
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=src
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=80
    --durations=10
    --color=yes

# Markers for test categorization
markers =
    unit: Unit tests (fast, isolated)
    integration: Integration tests (slower, external dependencies)
    slow: Slow tests (may take several seconds)
    workflow: Workflow-specific tests
    api: API endpoint tests
    ui: User interface tests
    llm: Tests requiring LLM providers
    docker: Tests requiring Docker
    network: Tests requiring network access
    
# Minimum version
minversion = 7.0

# Test session configuration
console_output_style = progress
junit_family = xunit2

# Warnings
filterwarnings =
    error
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*unclosed.*:ResourceWarning

# Asyncio configuration
asyncio_mode = auto

# Coverage configuration
[coverage:run]
source = src
omit = 
    */tests/*
    */test_*
    */__pycache__/*
    */venv/*
    */build/*
    */dist/*
    */examples/*
    setup.py
    setup_project.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError
    if __name__ == .__main__.:
    if TYPE_CHECKING:
    @abstract
    @abstractmethod
    
show_missing = true
precision = 2
skip_covered = false
skip_empty = true

[coverage:html]
directory = htmlcov
title = Blog Workflow LangGraph Coverage Report

[coverage:xml]
output = coverage.xml
