# 🚀 Blog Workflow LangGraph

> **Sistema avançado de criação automatizada de conteúdo para blogs usando LangGraph, com revisão humana integrada e otimização SEO**

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)
[![Tests](https://img.shields.io/badge/tests-passing-brightgreen.svg)](https://github.com/gabriel/blog-workflow-langgraph/actions)
[![Coverage](https://img.shields.io/badge/coverage-85%25-green.svg)](https://github.com/gabriel/blog-workflow-langgraph)
[![Docker](https://img.shields.io/badge/docker-ready-blue.svg)](https://hub.docker.com/)

## 🎯 **Portfolio Project - AI Engineering Showcase**

Este projeto demonstra expertise em:

- **LangGraph & Workflow Orchestration** - Implementação de grafos de estado complexos
- **Clean Architecture** - Separação clara de responsabilidades e SOLID principles
- **AI/LLM Integration** - Múltiplos provedores com fallback e error handling
- **Production-Ready Code** - Testes, CI/CD, Docker, observabilidade
- **Modern Python** - Type hints, Pydantic, async/await, context managers

---

## ✨ Principais Funcionalidades

- 🤖 **Múltiplos Provedores LLM**: OpenAI, Anthropic, Ollama e simulador integrado
- 🔄 **Workflow Inteligente**: Fluxo estruturado com validação e controle de qualidade
- 👥 **Revisão Humana**: Pontos de interrupção estratégicos para aprovação manual
- 🎯 **SEO Otimizado**: Geração automática de metadados e sugestões de otimização
- 🚀 **API REST**: Interface programática completa com FastAPI
- 💻 **Interface Web**: Dashboard interativo com Streamlit
- 🐳 **Docker Ready**: Containerização completa para deploy fácil
- 🧪 **Testes Abrangentes**: Cobertura completa com pytest

---

## 🚀 Instalação Rápida

### Opção 1: Setup Automático (Recomendado)

```bash
git clone https://github.com/gabriel/blog-workflow-langgraph.git
cd blog-workflow-langgraph
make quickstart-full  # Setup completo com configuração interativa
```

### Opção 2: Setup Rápido

```bash
git clone https://github.com/gabriel/blog-workflow-langgraph.git
cd blog-workflow-langgraph
make quickstart  # Setup básico com simulador
```

### Opção 3: Instalação Manual

```bash
git clone https://github.com/gabriel/blog-workflow-langgraph.git
cd blog-workflow-langgraph
pip install -e .
make create-env  # Criar arquivo .env
python test_simple.py  # Verificar instalação
```

---

## 🔄 Arquitetura do Sistema

![Workflow Diagram](workflow.svg)

O sistema implementa um workflow baseado em grafos com os seguintes componentes:

1. **Topic Researcher** - Pesquisa e valida tópicos
2. **Outline Generator** - Cria estrutura do conteúdo
3. **Content Writer** - Gera o conteúdo principal
4. **SEO Optimizer** - Otimiza para mecanismos de busca
5. **Human Review Gate** - Ponto de revisão humana
6. **Content Finalizer** - Finaliza e formata o resultado

---

## 💻 Exemplos de Uso

### Uso Básico - Simulador (Sem API Keys)

```python
from src.blog_workflow import BlogWorkflowManager, WorkflowConfig

# Configuração básica
config = WorkflowConfig(max_revisions=2)
workflow = BlogWorkflowManager(config)

# Executar workflow
result = workflow.run_workflow("Inteligência Artificial em 2024")
print(f"Status: {result.status}")
print(f"Conteúdo: {result.final_content}")
```

### Uso com LLMs Reais

```python
from src.config.llm_config import LLMConfig, LLMConfigManager

# Configurar OpenAI
config = LLMConfig(
    provider_type="openai",
    model="gpt-4",
    api_key="sua-chave-aqui"
)

# Ou usar variáveis de ambiente (.env)
# OPENAI_API_KEY=sua-chave-aqui
manager = LLMConfigManager()
config = manager.load_config()

workflow = BlogWorkflowManager()
result = workflow.run_workflow("Tendências de IA em 2024")
```

### API REST

```bash
# Iniciar servidor
make run-api
# ou
uvicorn api.main:app --reload

# Usar API
curl -X POST "http://localhost:8000/workflow/start" \
  -H "Content-Type: application/json" \
  -d '{"topic": "Machine Learning", "session_id": "test123"}'
```

### Interface Web

```bash
# Iniciar Streamlit
make run-ui
# ou
streamlit run apps/streamlit_app.py

# Acessar: http://localhost:8501
```

### Docker

```bash
# Build e executar
make docker-build
make docker-run

# Ou usar docker-compose
docker-compose up -d

# Acessar:
# API: http://localhost:8000
# UI: http://localhost:8501
```

---

## ⚙️ Configuração

### 🔧 Setup Interativo (Recomendado)

```bash
# Configuração interativa completa
make setup-env

# Ou criar .env básico e editar manualmente
make create-env
```

### 📝 Configuração Manual (.env)

Copie `.env.example` para `.env` e configure suas chaves:

```bash
cp .env.example .env
```

**Principais configurações:**

```bash
# === LLM Providers ===
# OpenAI
OPENAI_API_KEY=sk-your-openai-key-here
OPENAI_MODEL=gpt-4

# Anthropic
ANTHROPIC_API_KEY=sk-ant-your-anthropic-key-here
ANTHROPIC_MODEL=claude-3-sonnet-20240229

# Ollama (Local)
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2

# Provider padrão
LLM_PROVIDER=simulator  # openai, anthropic, ollama, simulator

# === Configurações do Workflow ===
MAX_REVISIONS=3
CONTENT_PREVIEW_LENGTH=500
ENABLE_HUMAN_REVIEW=true
AUTO_SEO_OPTIMIZATION=true

# === Sistema ===
LOG_LEVEL=INFO
DATABASE_PATH=./data/workflow.db
```

### 🔍 Verificar Configuração

```bash
# Verificar se a configuração está correta
make check-env

# Testar com simulador
python test_simple.py

# Testar com LLM real (se configurado)
python examples/real_llm_demo.py
```

### 💻 Configuração Programática

```python
from src.config.llm_config import create_config_from_env, LLMConfig
from src.blog_workflow import WorkflowConfig

# Carregar configuração do .env automaticamente
llm_config = create_config_from_env()

# Ou configurar manualmente
llm_config = LLMConfig(
    provider_type="openai",
    api_key="sua-chave-aqui",
    model="gpt-4"
)

# Configuração do workflow
workflow_config = WorkflowConfig(
    max_revisions=5,
    content_preview_length=1000
)
```

---

## 🧪 Testes

```bash
# Teste rápido
make test

# Testes completos
make test-full

# Com cobertura
make test-cov

# Linting
make lint
make format
```

---

## 🛠️ Estrutura do Projeto

```
blog-workflow-langgraph/
├── src/
│   ├── __init__.py
│   ├── blog_workflow.py          # Workflow principal
│   ├── agents/                   # Agentes especializados
│   │   ├── content_agents.py     # Agentes de conteúdo
│   │   └── seo_agents.py         # Agentes de SEO
│   ├── config/                   # Configurações
│   │   └── llm_config.py         # Config LLM
│   ├── integrations/             # Integrações externas
│   │   └── llm_provider.py       # Provedores LLM
│   ├── models/                   # Modelos de dados
│   │   └── workflow_state.py     # Estado do workflow
│   └── utils/                    # Utilitários
│       ├── helpers.py            # Funções auxiliares
│       └── llm_simulator.py      # Simulador LLM
├── api/                          # API REST
│   └── main.py                   # FastAPI app
├── apps/                         # Aplicações
│   └── streamlit_app.py          # Interface Streamlit
├── examples/                     # Exemplos de uso
├── tests/                        # Testes
├── Dockerfile                    # Container principal
├── docker-compose.yml            # Orquestração
├── Makefile                      # Comandos automatizados
└── pyproject.toml               # Configuração do projeto
```

---

## 🚀 Desenvolvimento

### Setup do Ambiente de Desenvolvimento

```bash
pip install -e ".[dev]"
pytest tests/
pytest --cov=src tests/
```

---

## 📋 Roadmap

### Versão 1.1 (Atual)

- [x] Sistema de workflow básico
- [x] Múltiplos provedores LLM
- [x] Simulador integrado
- [x] Testes abrangentes
- [x] Documentação completa

### Versão 2.1 (Próxima)

- [ ] Integração com LLMs reais (OpenAI, Anthropic)
- [ ] Interface web com Streamlit
- [ ] Templates personalizáveis
- [ ] Análise de sentimento

---

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

---

## 📝 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para detalhes.

---

## 🆘 Suporte e Troubleshooting

### Problemas Comuns

**1. Erro de importação**

```bash
pip install -e .
python -c "import src.blog_workflow; print('OK')"
```

**2. Workflow não inicia**

```python
import langgraph  # Deve importar sem erro
```

**3. Sessão não persiste**

- Verifique se o diretório `data/` existe
- Confirme permissões de escrita

### Logs e Debug

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

---

## 👨‍💻 Autor

**Gabriel** - [GitHub](https://github.com/gabriel)

---

## 🙏 Agradecimentos

- [LangGraph](https://github.com/langchain-ai/langgraph) - Framework de workflows
- [FastAPI](https://fastapi.tiangolo.com/) - Framework web moderno
- [Streamlit](https://streamlit.io/) - Interface web interativa
- [Pydantic](https://pydantic-docs.helpmanual.io/) - Validação de dados

---

<div align="center">

**⭐ Se este projeto foi útil, considere dar uma estrela! ⭐**

</div>
