# 🚀 Deployment Guide

This guide covers deployment strategies for Blog Workflow LangGraph in various environments.

## 🐳 Docker Deployment (Recommended)

### Quick Start
```bash
# Clone and setup
git clone https://github.com/gabriel/blog-workflow-langgraph.git
cd blog-workflow-langgraph

# Configure environment
cp .env.example .env
# Edit .env with your API keys

# Deploy with Docker Compose
docker-compose up -d

# Verify deployment
curl http://localhost:8000/health
curl http://localhost:8501
```

### Production Docker Deployment
```bash
# Build production images
docker-compose build

# Deploy with resource limits
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Monitor logs
docker-compose logs -f
```

## ☁️ Cloud Deployment

### AWS ECS Deployment
```bash
# Build and push to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin <account>.dkr.ecr.us-east-1.amazonaws.com

docker build -f Dockerfile.api -t blog-workflow-api .
docker tag blog-workflow-api:latest <account>.dkr.ecr.us-east-1.amazonaws.com/blog-workflow-api:latest
docker push <account>.dkr.ecr.us-east-1.amazonaws.com/blog-workflow-api:latest

# Deploy with ECS CLI or CloudFormation
```

### Google Cloud Run
```bash
# Build and deploy
gcloud builds submit --tag gcr.io/PROJECT-ID/blog-workflow-api
gcloud run deploy --image gcr.io/PROJECT-ID/blog-workflow-api --platform managed
```

### Azure Container Instances
```bash
# Create resource group and deploy
az group create --name blog-workflow --location eastus
az container create --resource-group blog-workflow --name blog-workflow-api --image blog-workflow-api:latest
```

## 🔧 Environment Configuration

### Production Environment Variables
```bash
# Core Configuration
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
WORKER_PROCESSES=4

# Database
DATABASE_PATH=/app/data/workflow.db

# Security
SECRET_KEY=your-production-secret-key
ALLOWED_HOSTS=yourdomain.com,api.yourdomain.com

# LLM Providers
OPENAI_API_KEY=your-openai-key
ANTHROPIC_API_KEY=your-anthropic-key
LLM_PROVIDER=openai

# Monitoring
ENABLE_METRICS=true
LOG_FILE=/app/logs/workflow.log
```

## 📊 Monitoring & Observability

### Health Checks
```bash
# API Health
curl http://localhost:8000/health

# Detailed Status
curl http://localhost:8000/status

# Metrics (if enabled)
curl http://localhost:9090/metrics
```

### Logging Configuration
```yaml
# docker-compose.prod.yml
version: "3.8"
services:
  blog-workflow-api:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

## 🔒 Security Considerations

### Production Security Checklist
- [ ] Use HTTPS/TLS certificates
- [ ] Configure firewall rules
- [ ] Set up API rate limiting
- [ ] Enable security headers
- [ ] Use secrets management
- [ ] Regular security updates
- [ ] Monitor for vulnerabilities

### Secrets Management
```bash
# Using Docker secrets
echo "your-api-key" | docker secret create openai_api_key -

# Using environment files
docker-compose --env-file .env.prod up -d
```

## 📈 Scaling

### Horizontal Scaling
```yaml
# docker-compose.scale.yml
version: "3.8"
services:
  blog-workflow-api:
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
```

### Load Balancing
```nginx
# nginx.conf
upstream blog_workflow_api {
    server api1:8000;
    server api2:8000;
    server api3:8000;
}

server {
    listen 80;
    location / {
        proxy_pass http://blog_workflow_api;
    }
}
```

## 🔄 CI/CD Integration

### GitHub Actions Deployment
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Deploy to production
        run: |
          # Your deployment script
          docker-compose -f docker-compose.prod.yml up -d
```

## 🛠️ Troubleshooting

### Common Issues
1. **Port conflicts**: Check if ports 8000/8501 are available
2. **Permission errors**: Ensure proper file permissions
3. **Memory issues**: Increase Docker memory limits
4. **API key errors**: Verify environment variables

### Debug Commands
```bash
# Check container status
docker-compose ps

# View logs
docker-compose logs blog-workflow-api

# Execute commands in container
docker-compose exec blog-workflow-api bash

# Test API endpoints
curl -v http://localhost:8000/workflows
```

## 📋 Maintenance

### Regular Maintenance Tasks
- Monitor disk space and logs
- Update dependencies regularly
- Backup workflow data
- Review security logs
- Performance monitoring

### Backup Strategy
```bash
# Backup workflow data
docker-compose exec blog-workflow-api tar -czf /tmp/backup.tar.gz /app/data

# Copy backup from container
docker cp $(docker-compose ps -q blog-workflow-api):/tmp/backup.tar.gz ./backup-$(date +%Y%m%d).tar.gz
```
