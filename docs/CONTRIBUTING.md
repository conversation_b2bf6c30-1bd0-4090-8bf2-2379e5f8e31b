# 🤝 Contributing to Blog Workflow LangGraph

Thank you for your interest in contributing to Blog Workflow LangGraph! This document provides comprehensive guidelines for contributing to this AI engineering portfolio project.

## 🎯 Project Vision

This project demonstrates professional AI engineering practices and serves as a portfolio piece showcasing:

- Clean architecture and SOLID principles
- Production-ready LLM integration patterns
- Comprehensive testing and CI/CD
- Modern Python development practices

## 🚀 Quick Start

### Prerequisites

- Python 3.8+ (3.11+ recommended)
- Git
- Docker (optional, for containerized development)

### Setup Development Environment

```bash
# 1. Fork and clone the repository
git clone https://github.com/yourusername/blog-workflow-langgraph.git
cd blog-workflow-langgraph

# 2. Set up development environment
make install-dev
make setup-env  # Interactive environment setup

# 3. Verify installation
make test
make lint
```

### Alternative Setup with Docker

```bash
# Development with Docker
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
```

## 🔄 Development Workflow

### 1. Create Feature Branch

```bash
git checkout -b feature/your-feature-name
# or
git checkout -b fix/bug-description
# or
git checkout -b docs/documentation-update
```

### 2. Development Process

```bash
# Install pre-commit hooks (recommended)
pre-commit install

# Make your changes
# ... code, code, code ...

# Run quality checks
make lint          # Code formatting and linting
make test          # Run test suite
make test-cov      # Run tests with coverage
```

### 3. Commit Guidelines

We follow [Conventional Commits](https://www.conventionalcommits.org/):

```bash
# Format: type(scope): description
git commit -m "feat(agents): add content optimization agent"
git commit -m "fix(api): resolve workflow state persistence issue"
git commit -m "docs(readme): update installation instructions"
git commit -m "test(workflow): add integration tests for review flow"
```

**Commit Types:**

- `feat`: New features
- `fix`: Bug fixes
- `docs`: Documentation changes
- `test`: Test additions/modifications
- `refactor`: Code refactoring
- `perf`: Performance improvements
- `ci`: CI/CD changes
- `chore`: Maintenance tasks

### 4. Pull Request Process

```bash
# Push your branch
git push origin feature/your-feature-name

# Create PR with:
# - Clear title and description
# - Reference to related issues
# - Screenshots/demos if applicable
# - Checklist completion
```

## 📋 Code Standards

### Code Style

```bash
# Formatting (automatic with pre-commit)
black src/ tests/ examples/
isort src/ tests/ examples/

# Linting
flake8 src/ tests/ examples/
mypy src/

# Security scanning
bandit -r src/
```

### Code Quality Requirements

- **Type Hints**: All functions must have type annotations
- **Docstrings**: Google-style docstrings for all public APIs
- **Error Handling**: Explicit error handling with custom exceptions
- **Logging**: Structured logging with appropriate levels
- **Testing**: Minimum 80% test coverage

### Example Code Style

```python
from typing import Optional, Dict, Any
from pydantic import BaseModel
import structlog

logger = structlog.get_logger(__name__)

class WorkflowConfig(BaseModel):
    """Configuration for blog workflow execution.

    Args:
        max_revisions: Maximum number of content revisions allowed
        timeout: Workflow timeout in seconds
        enable_seo: Whether to enable SEO optimization

    Example:
        >>> config = WorkflowConfig(max_revisions=3, timeout=300)
        >>> workflow = BlogWorkflow(config)
    """
    max_revisions: int = 3
    timeout: int = 300
    enable_seo: bool = True

async def process_workflow(
    topic: str,
    config: WorkflowConfig,
    session_id: Optional[str] = None
) -> Dict[str, Any]:
    """Process blog workflow for given topic.

    Args:
        topic: Blog topic to process
        config: Workflow configuration
        session_id: Optional session identifier

    Returns:
        Dictionary containing workflow results

    Raises:
        WorkflowError: If workflow processing fails
        ValidationError: If input validation fails
    """
    logger.info("Starting workflow", topic=topic, session_id=session_id)

    try:
        # Implementation here
        result = await _execute_workflow(topic, config)
        logger.info("Workflow completed", result_status=result.get("status"))
        return result
    except Exception as e:
        logger.error("Workflow failed", error=str(e), topic=topic)
        raise WorkflowError(f"Failed to process workflow: {e}") from e
```

## 🧪 Testing Guidelines

### Test Structure

```
tests/
├── unit/           # Fast, isolated unit tests
├── integration/    # Integration tests with external dependencies
├── e2e/           # End-to-end workflow tests
├── fixtures/      # Test data and fixtures
└── conftest.py    # Pytest configuration
```

### Writing Tests

```python
import pytest
from unittest.mock import Mock, patch
from src.blog_workflow import BlogWorkflow, WorkflowConfig

class TestBlogWorkflow:
    """Test suite for BlogWorkflow class."""

    @pytest.fixture
    def workflow_config(self) -> WorkflowConfig:
        """Create test workflow configuration."""
        return WorkflowConfig(max_revisions=2, timeout=60)

    @pytest.fixture
    def mock_llm_provider(self) -> Mock:
        """Create mock LLM provider."""
        mock = Mock()
        mock.generate_content.return_value = "Generated content"
        return mock

    @pytest.mark.unit
    async def test_workflow_initialization(self, workflow_config):
        """Test workflow initializes correctly."""
        workflow = BlogWorkflow(workflow_config)
        assert workflow.config.max_revisions == 2
        assert workflow.config.timeout == 60

    @pytest.mark.integration
    @patch('src.integrations.llm_provider.OpenAIProvider')
    async def test_workflow_execution(self, mock_provider, workflow_config):
        """Test complete workflow execution."""
        # Setup
        mock_provider.return_value.generate_content.return_value = "Test content"
        workflow = BlogWorkflow(workflow_config)

        # Execute
        result = await workflow.run("Test topic")

        # Assert
        assert result["status"] == "completed"
        assert "content" in result
        mock_provider.return_value.generate_content.assert_called()

    @pytest.mark.slow
    async def test_workflow_with_real_llm(self, workflow_config):
        """Test workflow with real LLM (requires API key)."""
        pytest.skip("Requires LLM API key")
```

### Test Categories

- `@pytest.mark.unit`: Fast, isolated tests
- `@pytest.mark.integration`: Tests with external dependencies
- `@pytest.mark.slow`: Long-running tests
- `@pytest.mark.workflow`: Workflow-specific tests
- `@pytest.mark.api`: API endpoint tests

## 📚 Documentation Standards

### Code Documentation

- **Docstrings**: Google-style for all public APIs
- **Type Hints**: Complete type annotations
- **Examples**: Include usage examples in docstrings
- **Error Documentation**: Document all raised exceptions

### Project Documentation

- **README**: Keep installation and usage instructions current
- **API Docs**: Auto-generated from docstrings
- **Architecture**: Document design decisions
- **Examples**: Provide working code examples
