import logging
import json
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass
from pathlib import Path

try:
    from langgraph.graph import StateGraph, END
    from langgraph.checkpoint.sqlite import SqliteSaver
    LANGGRAPH_AVAILABLE = True
except ImportError:
    # Fallback for when langgraph is not available
    LANGGRAPH_AVAILABLE = False
    StateGraph = None
    SqliteSaver = None
    END = "END"

# Relative imports for models and agents
from .models.workflow_state import BlogWorkflowState, NodeNames, ReviewStatus, ReviewFeedback
from .agents.content_agents import (
    topic_researcher_agent,
    outline_generator_agent,
    content_writer_agent,
    human_review_node,
    finalizer_agent
)
from .agents.seo_agents import seo_optimizer_agent

logger = logging.getLogger(__name__)

# --- Custom Exceptions ---

class WorkflowError(Exception):
    """Base exception for workflow errors"""
    pass

class WorkflowConfigurationError(WorkflowError):
    """Raised when workflow configuration is invalid"""
    pass

class WorkflowExecutionError(WorkflowError):
    """Raised when workflow execution fails"""
    pass

class MaxRevisionsExceededError(WorkflowError):
    """Raised when maximum revisions limit is exceeded"""
    pass

# --- Configuration Classes ---

@dataclass
class WorkflowConfig:
    """Configuration class for workflow parameters"""
    max_revisions: int = 3
    memory_path: str = ":memory:"
    default_thread_id: str = "default_thread"
    content_preview_length: int = 500

    def __post_init__(self):
        """Validate configuration after initialization"""
        if self.max_revisions < 1:
            raise WorkflowConfigurationError("max_revisions must be at least 1")
        if self.content_preview_length < 100:
            raise WorkflowConfigurationError("content_preview_length must be at least 100")

@dataclass
class WorkflowStatus:
    """Data class for workflow status information"""
    current_step: str
    topic: str = ""
    revision_count: int = 0
    human_approved: bool = False
    error_message: Optional[str] = None
    thread_id: str = ""

    @property
    def has_error(self) -> bool:
        return bool(self.error_message)

    @property
    def is_completed(self) -> bool:
        return self.current_step == "finalizer"

# --- Router Classes ---

class WorkflowRouter:
    """Handles routing logic for the workflow"""

    def __init__(self, config: WorkflowConfig):
        self.config = config

    def route_after_review(self, state: BlogWorkflowState) -> str:
        """Enhanced routing logic after human review"""
        logger.info("🤔 Analyzing review feedback")

        feedback = state.get("review_feedback")
        if not feedback:
            logger.error("No feedback found - this should not happen after interrupt")
            raise WorkflowExecutionError("Missing review feedback in state")

        if feedback.status == ReviewStatus.APPROVED:
            logger.info("✅ Approved - proceeding to SEO optimization")
            return NodeNames.SEO_OPTIMIZER.value

        elif feedback.status == ReviewStatus.NEEDS_REVISION:
            revision_count = state.get("revision_count", 0)

            if revision_count >= self.config.max_revisions:
                logger.warning(f"⚠️ Maximum revisions ({self.config.max_revisions}) reached - terminating")
                raise MaxRevisionsExceededError(f"Maximum revisions limit ({self.config.max_revisions}) exceeded")

            logger.info(f"🔄 Needs revision (attempt {revision_count + 1}) - returning to content writer")
            return NodeNames.CONTENT_WRITER.value

        else:  # REJECTED
            logger.info("❌ Rejected - terminating workflow")
            return END

# --- Input Handler Classes ---

class HumanReviewHandler:
    """Handles human review input and validation"""

    VALID_DECISIONS = {"aprovar", "revisar", "rejeitar"}

    def __init__(self, config: WorkflowConfig):
        self.config = config

    def get_user_decision(self) -> str:
        """Get and validate user decision"""
        while True:
            print("\n" + "="*60)
            print("🔍 HUMAN REVIEW PROCESS")
            print("="*60)
            print("\nReview options:")
            print("1. 'aprovar' - Approve and continue to SEO")
            print("2. 'revisar' - Request revision with comments")
            print("3. 'rejeitar' - Reject and terminate workflow")

            decision = input("\nYour decision (aprovar/revisar/rejeitar): ").strip().lower()

            if decision in self.VALID_DECISIONS:
                return decision

            print(f"❌ Invalid decision: '{decision}'. Please choose from: {', '.join(self.VALID_DECISIONS)}")

    def create_feedback(self, decision: str) -> ReviewFeedback:
        """Create feedback based on user decision"""
        if decision == "aprovar":
            return ReviewFeedback(
                status=ReviewStatus.APPROVED,
                comments="Approved by human reviewer."
            )

        elif decision == "revisar":
            comments = input("Comments for revision: ").strip()
            return ReviewFeedback(
                status=ReviewStatus.NEEDS_REVISION,
                comments=comments or "General revision needed."
            )

        else:  # rejeitar
            return ReviewFeedback(
                status=ReviewStatus.REJECTED,
                comments="Rejected by human reviewer."
            )

    def display_content_preview(self, state_values: Dict[str, Any]) -> None:
        """Display content preview for review"""
        print("\n" + "="*60)
        print("📋 CONTENT READY FOR REVIEW")
        print("="*60)
        print(f"Topic: {state_values.get('topic', 'N/A')}")
        print(f"Review #: {state_values.get('revision_count', 0) + 1}")

        content = state_values.get('draft_content', '')
        if content:
            print(f"\nFirst {self.config.content_preview_length} characters of content:")
            print("-" * 40)
            print(content[:self.config.content_preview_length] + "...")
            print("-" * 40)
        else:
            print("\n⚠️ No content available for preview")

# --- Main Workflow Manager ---

class BlogWorkflowManager:
    """Enhanced workflow manager with better separation of concerns"""

    def __init__(self, config: Optional[WorkflowConfig] = None):
        """Initialize the workflow manager"""
        self.config = config or WorkflowConfig()
        self.router = WorkflowRouter(self.config)
        self.review_handler = HumanReviewHandler(self.config)

        # Simple in-memory storage for when LangGraph is not available
        self._simple_storage = {}

        try:
            if LANGGRAPH_AVAILABLE:
                self.memory = SqliteSaver.from_conn_string(self.config.memory_path)
                self.app = self._build_workflow()
                logger.info("🚀 Workflow Manager initialized successfully")
            else:
                logger.warning("LangGraph not available, using simplified workflow")
                self.memory = None
                self.app = None
        except Exception as e:
            logger.error(f"Failed to initialize workflow manager: {e}")
            raise WorkflowConfigurationError(f"Initialization failed: {e}")

    def _build_workflow(self) -> StateGraph:
        """Build the workflow graph with proper error handling"""
        try:
            workflow = StateGraph(BlogWorkflowState)

            # Add all nodes
            self._add_workflow_nodes(workflow)

            # Set entry point
            workflow.set_entry_point(NodeNames.TOPIC_RESEARCHER.value)

            # Add edges
            self._add_workflow_edges(workflow)

            # Compile with checkpoint and interrupts
            return workflow.compile(
                checkpointer=self.memory,
                interrupt_before_nodes=[NodeNames.HUMAN_REVIEW_GATE.value]
            )

        except Exception as e:
            logger.error(f"Failed to build workflow: {e}")
            raise WorkflowConfigurationError(f"Workflow building failed: {e}")

    def _add_workflow_nodes(self, workflow: StateGraph) -> None:
        """Add all nodes to the workflow"""
        nodes = [
            (NodeNames.TOPIC_RESEARCHER.value, topic_researcher_agent),
            (NodeNames.OUTLINE_GENERATOR.value, outline_generator_agent),
            (NodeNames.CONTENT_WRITER.value, content_writer_agent),
            (NodeNames.HUMAN_REVIEW_GATE.value, human_review_node),
            (NodeNames.SEO_OPTIMIZER.value, seo_optimizer_agent),
            (NodeNames.FINALIZER.value, finalizer_agent),
        ]

        for node_name, node_func in nodes:
            workflow.add_node(node_name, node_func)

    def _add_workflow_edges(self, workflow: StateGraph) -> None:
        """Add all edges to the workflow"""
        # Linear edges
        linear_edges = [
            (NodeNames.TOPIC_RESEARCHER.value, NodeNames.OUTLINE_GENERATOR.value),
            (NodeNames.OUTLINE_GENERATOR.value, NodeNames.CONTENT_WRITER.value),
            (NodeNames.CONTENT_WRITER.value, NodeNames.HUMAN_REVIEW_GATE.value),
            (NodeNames.SEO_OPTIMIZER.value, NodeNames.FINALIZER.value),
            (NodeNames.FINALIZER.value, END),
        ]

        for source, target in linear_edges:
            workflow.add_edge(source, target)

        # Conditional edge after review
        workflow.add_conditional_edges(
            NodeNames.HUMAN_REVIEW_GATE.value,
            self.router.route_after_review,
            {
                NodeNames.SEO_OPTIMIZER.value: NodeNames.SEO_OPTIMIZER.value,
                NodeNames.CONTENT_WRITER.value: NodeNames.CONTENT_WRITER.value,
                END: END
            }
        )

    def run_workflow(self, thread_id: Optional[str] = None, topic: Optional[str] = None) -> Dict[str, Any]:
        """Execute the complete workflow with proper error handling"""
        thread_id = thread_id or self.config.default_thread_id
        config = {"configurable": {"thread_id": thread_id}}

        logger.info(f"🎬 Starting workflow for thread: {thread_id}")

        # Check if LangGraph is available
        if not self.app:
            # Use simplified workflow when LangGraph is not available
            if topic:
                return self._run_simplified_workflow(topic, thread_id)
            else:
                logger.error("No topic provided and LangGraph not available")
                return {"error_message": "Topic is required for simplified workflow", "current_step": "error"}

        # Initialize state with topic if provided
        if topic:
            from .models.workflow_state import create_initial_state
            initial_state = create_initial_state(topic)

            # Update state with initial values
            self.app.update_state(config, initial_state)
            logger.info(f"📝 Initialized workflow with topic: {topic}")

        try:
            # Execute until first interrupt
            self._run_until_interrupt(config)

            # Handle human review
            self._handle_human_review(config)

            # Continue and complete workflow
            return self._complete_workflow(config)

        except MaxRevisionsExceededError as e:
            logger.warning(f"Workflow terminated: {e}")
            return {"error_message": str(e), "current_step": "max_revisions_exceeded"}

        except WorkflowExecutionError as e:
            logger.error(f"Workflow execution failed: {e}")
            return {"error_message": str(e), "current_step": "execution_error"}

        except Exception as e:
            logger.error(f"Unexpected error during workflow execution: {e}", exc_info=True)
            return {"error_message": f"Unexpected error: {e}", "current_step": "unexpected_error"}

    def _run_until_interrupt(self, config: Dict[str, Any]) -> None:
        """Run workflow until the first interrupt point"""
        logger.info("▶️ Executing until first interrupt (human review)...")

        for event in self.app.stream(None, config=config, stream_mode="values"):
            current_step_details = list(event.values())[0]
            step_name = current_step_details.get("current_step", "unknown_step")
            logger.info(f"🔄 Stream step: {step_name}")

    def _handle_human_review(self, config: Dict[str, Any]) -> None:
        """Handle human review process with proper error handling"""
        logger.info("👤 Starting human feedback process")

        try:
            current_state = self.app.get_state(config)
            if not current_state:
                raise WorkflowExecutionError("Unable to retrieve current state for review")

            state_values = current_state.values
            revision_count = state_values.get("revision_count", 0)

            # Display content for review
            self.review_handler.display_content_preview(state_values)

            # Get user decision
            decision = self.review_handler.get_user_decision()

            # Create feedback
            feedback = self.review_handler.create_feedback(decision)

            # Update state
            update_payload = {
                "review_feedback": feedback,
                "human_approved": feedback.status == ReviewStatus.APPROVED,
                "revision_count": revision_count + 1 if feedback.status == ReviewStatus.NEEDS_REVISION else revision_count
            }

            self.app.update_state(config, update_payload)
            logger.info(f"✅ Feedback applied: {feedback.status.value}. Revision count: {update_payload['revision_count']}")

        except Exception as e:
            logger.error(f"Error during human review: {e}")
            raise WorkflowExecutionError(f"Human review failed: {e}")

    def _complete_workflow(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Complete the workflow execution after review"""
        logger.info("▶️ Continuing workflow after feedback...")

        final_result = None
        for event in self.app.stream(None, config=config, stream_mode="values"):
            current_step_details = list(event.values())[0]
            step_name = current_step_details.get("current_step", "unknown_step")
            logger.info(f"🔄 Post-review stream step: {step_name}")
            final_result = current_step_details

        if not final_result:
            logger.warning("Workflow did not produce final result")
            return self.app.get_state(config).values

        return final_result

    def get_workflow_status(self, thread_id: Optional[str] = None) -> WorkflowStatus:
        """Get current workflow status with proper error handling"""
        thread_id = thread_id or self.config.default_thread_id
        config = {"configurable": {"thread_id": thread_id}}

        try:
            if self.app:
                state = self.app.get_state(config)

                if not state:
                    logger.warning(f"No state found for thread_id: {thread_id}")
                    return WorkflowStatus(
                        current_step="not_started",
                        error_message="No state found for this thread_id",
                        thread_id=thread_id
                    )

                return WorkflowStatus(
                    current_step=state.values.get("current_step", "not_started"),
                    topic=state.values.get("topic", ""),
                    revision_count=state.values.get("revision_count", 0),
                    human_approved=state.values.get("human_approved", False),
                    error_message=state.values.get("error_message"),
                    thread_id=thread_id
                )
            else:
                # Use simple storage when LangGraph is not available
                logger.info(f"Getting simplified status for thread: {thread_id}")
                stored_state = self._simple_storage.get(thread_id, {})
                return WorkflowStatus(
                    current_step=stored_state.get("current_step", "ready"),
                    topic=stored_state.get("topic", ""),
                    revision_count=stored_state.get("revision_count", 0),
                    human_approved=stored_state.get("human_approved", False),
                    error_message=stored_state.get("error_message"),
                    thread_id=thread_id
                )

        except Exception as e:
            logger.error(f"Error getting workflow status: {e}")
            return WorkflowStatus(
                current_step="error",
                error_message=f"Failed to get status: {e}",
                thread_id=thread_id
            )

    def reset_workflow(self, thread_id: Optional[str] = None) -> bool:
        """Reset workflow state for a given thread"""
        thread_id = thread_id or self.config.default_thread_id

        try:
            # This would depend on LangGraph's API for state clearing
            # For now, we'll log the intention
            logger.info(f"🔄 Resetting workflow for thread: {thread_id}")
            # Implementation would depend on LangGraph's state management capabilities
            return True
        except Exception as e:
            logger.error(f"Failed to reset workflow: {e}")
            return False

    def get_workflow_state(self, thread_id: Optional[str] = None) -> Dict[str, Any]:
        """Get the current workflow state"""
        thread_id = thread_id or self.config.default_thread_id
        config = {"configurable": {"thread_id": thread_id}}

        try:
            if self.app:
                state = self.app.get_state(config)
                return state.values if state else {}
            else:
                # Use simple storage when LangGraph is not available
                logger.info(f"Getting simplified state for thread: {thread_id}")
                return self._simple_storage.get(thread_id, {
                    "thread_id": thread_id,
                    "current_step": "ready",
                    "topic": None,
                    "revision_count": 0,
                    "human_approved": False,
                    "workflow_completed": False
                })
        except Exception as e:
            logger.error(f"Error getting workflow state: {e}")
            return {
                "thread_id": thread_id,
                "error_message": str(e),
                "current_step": "error"
            }

    def submit_human_review(self, thread_id: str, review_feedback: ReviewFeedback) -> Dict[str, Any]:
        """Submit human review feedback"""
        config = {"configurable": {"thread_id": thread_id}}

        try:
            if not self.app:
                logger.warning("No app available - using simplified workflow")
                return {"error": "Workflow not available"}

            # Get current state
            current_state = self.app.get_state(config)
            if not current_state:
                raise WorkflowExecutionError("Unable to retrieve current state for review")

            state_values = current_state.values
            revision_count = state_values.get("revision_count", 0)

            # Update state with review feedback
            update_payload = {
                "review_feedback": review_feedback,
                "human_approved": review_feedback.status == ReviewStatus.APPROVED,
                "revision_count": revision_count + 1 if review_feedback.status == ReviewStatus.REVISION_REQUESTED else revision_count
            }

            self.app.update_state(config, update_payload)
            logger.info(f"✅ Review feedback applied: {review_feedback.status.value}")

            # Continue workflow execution
            final_result = None
            for event in self.app.stream(None, config=config, stream_mode="values"):
                current_step_details = list(event.values())[0]
                step_name = current_step_details.get("current_step", "unknown_step")
                logger.info(f"🔄 Post-review stream step: {step_name}")
                final_result = current_step_details

            return final_result or self.app.get_state(config).values

        except Exception as e:
            logger.error(f"Error submitting human review: {e}")
            return {"error": str(e)}

    def list_workflows(self) -> List[Dict[str, Any]]:
        """List all workflows (simplified implementation)"""
        try:
            # This is a simplified implementation
            # In a real scenario, you'd query the database for all thread_ids
            logger.info("📋 Listing workflows (simplified)")
            return []
        except Exception as e:
            logger.error(f"Error listing workflows: {e}")
            return []

    def delete_workflow(self, thread_id: str) -> bool:
        """Delete a workflow (simplified implementation)"""
        try:
            logger.info(f"🗑️ Deleting workflow: {thread_id}")
            # This would involve clearing the state from the database
            # For now, just log the action
            return True
        except Exception as e:
            logger.error(f"Error deleting workflow: {e}")
            return False

    def _run_simplified_workflow(self, topic: str, thread_id: str) -> Dict[str, Any]:
        """Run a simplified workflow without LangGraph"""
        logger.info(f"🔧 Running simplified workflow for topic: {topic}")

        try:
            from .models.workflow_state import create_initial_state
            from .agents.content_agents import (
                topic_researcher_agent,
                outline_generator_agent,
                content_writer_agent,
                set_llm_provider
            )
            from .agents.seo_agents import seo_optimizer_agent
            from .integrations.llm_provider import SimulatorProvider

            # Set up simulator provider
            provider = SimulatorProvider()
            set_llm_provider(provider)

            # Initialize state
            state = create_initial_state(topic)
            state["thread_id"] = thread_id

            # Run agents in sequence
            logger.info("🔍 Running topic researcher...")
            state = topic_researcher_agent(state)

            if state.get("error_message"):
                return state

            logger.info("📝 Running outline generator...")
            state = outline_generator_agent(state)

            if state.get("error_message"):
                return state

            logger.info("✍️ Running content writer...")
            state = content_writer_agent(state)

            if state.get("error_message"):
                return state

            logger.info("🎯 Running SEO optimizer...")
            state = seo_optimizer_agent(state)

            if state.get("error_message"):
                return state

            # Finalize
            from .agents.content_agents import finalizer_agent
            logger.info("🏁 Running finalizer...")
            state = finalizer_agent(state)

            logger.info("✅ Simplified workflow completed successfully")

            # Store the final state in simple storage
            self._simple_storage[thread_id] = state

            return state

        except Exception as e:
            logger.error(f"Error in simplified workflow: {e}")
            return {
                "error_message": f"Simplified workflow failed: {str(e)}",
                "current_step": "simplified_workflow_error",
                "topic": topic,
                "thread_id": thread_id
            }

# --- Factory Functions ---

def create_workflow_manager(
    max_revisions: int = 3,
    memory_path: str = ":memory:",
    **kwargs
) -> BlogWorkflowManager:
    """Factory function to create a workflow manager with custom configuration"""
    config = WorkflowConfig(
        max_revisions=max_revisions,
        memory_path=memory_path,
        **kwargs
    )
    return BlogWorkflowManager(config)

def create_persistent_workflow_manager(
    db_path: str,
    max_revisions: int = 3,
    **kwargs
) -> BlogWorkflowManager:
    """Factory function to create a workflow manager with persistent storage"""
    # Ensure the directory exists
    db_file = Path(db_path)
    db_file.parent.mkdir(parents=True, exist_ok=True)

    return create_workflow_manager(
        max_revisions=max_revisions,
        memory_path=str(db_file),
        **kwargs
    )
