import os
import logging
from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
import json

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    _DOTENV_LOADED = True
    logging.debug("Environment variables loaded from .env file")
except ImportError:
    _DOTENV_LOADED = False
    logging.warning("python-dotenv not installed. Environment variables from .env file will not be loaded automatically.")

from ..integrations.llm_provider import ProviderType, get_available_providers

logger = logging.getLogger(__name__)

class ConfigError(Exception):
    """Custom exception for configuration errors"""
    pass

class ValidationError(ConfigError):
    """Exception for validation errors"""
    pass

@dataclass
class LLMConfig:
    """Configuration for LLM providers with validation and defaults"""

    provider_type: str = ProviderType.SIMULATOR.value
    model: str = ""
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    temperature: float = 0.7
    max_tokens: int = 4000
    timeout: int = 60
    extra_params: Dict[str, Any] = field(default_factory=dict)

    # Constants for validation
    MIN_TEMPERATURE = 0.0
    MAX_TEMPERATURE = 2.0
    MIN_TOKENS = 1
    MAX_TOKENS_LIMIT = 100000
    MIN_TIMEOUT = 1
    MAX_TIMEOUT = 300

    def __post_init__(self):
        """Validate configuration after initialization"""
        self._validate_provider_type()
        self._validate_temperature()
        self._validate_max_tokens()
        self._validate_timeout()

        # Set default model if not provided
        if not self.model:
            self.model = self._get_default_model()

    def _validate_provider_type(self) -> None:
        """Validate provider type"""
        available_providers = [p.value for p in ProviderType]
        if self.provider_type not in available_providers:
            raise ValidationError(
                f"Invalid provider type: {self.provider_type}. "
                f"Available providers: {', '.join(available_providers)}"
            )

    def _validate_temperature(self) -> None:
        """Validate temperature parameter"""
        if not isinstance(self.temperature, (int, float)):
            raise ValidationError("Temperature must be a number")

        if not (self.MIN_TEMPERATURE <= self.temperature <= self.MAX_TEMPERATURE):
            raise ValidationError(
                f"Temperature must be between {self.MIN_TEMPERATURE} and {self.MAX_TEMPERATURE}, "
                f"got {self.temperature}"
            )

    def _validate_max_tokens(self) -> None:
        """Validate max_tokens parameter"""
        if not isinstance(self.max_tokens, int):
            raise ValidationError("max_tokens must be an integer")

        if not (self.MIN_TOKENS <= self.max_tokens <= self.MAX_TOKENS_LIMIT):
            raise ValidationError(
                f"max_tokens must be between {self.MIN_TOKENS} and {self.MAX_TOKENS_LIMIT}, "
                f"got {self.max_tokens}"
            )

    def _validate_timeout(self) -> None:
        """Validate timeout parameter"""
        if not isinstance(self.timeout, int):
            raise ValidationError("timeout must be an integer")

        if not (self.MIN_TIMEOUT <= self.timeout <= self.MAX_TIMEOUT):
            raise ValidationError(
                f"timeout must be between {self.MIN_TIMEOUT} and {self.MAX_TIMEOUT} seconds, "
                f"got {self.timeout}"
            )

    def _get_default_model(self) -> str:
        """Get default model for provider type"""
        default_models = {
            ProviderType.OPENAI.value: "gpt-4",
            ProviderType.ANTHROPIC.value: "claude-3-sonnet-20240229",
            ProviderType.OLLAMA.value: "llama2",
            ProviderType.SIMULATOR.value: "simulator"
        }
        return default_models.get(self.provider_type, "default")

    def to_provider_kwargs(self) -> Dict[str, Any]:
        """Convert config to provider constructor kwargs"""
        kwargs = {
            "model": self.model,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "timeout": self.timeout,
            **self.extra_params
        }

        if self.api_key:
            kwargs["api_key"] = self.api_key

        if self.base_url:
            kwargs["base_url"] = self.base_url

        return kwargs

    def to_dict(self, hide_sensitive: bool = True) -> Dict[str, Any]:
        """Convert to dictionary for serialization

        Args:
            hide_sensitive: Whether to hide sensitive information like API keys
        """
        api_key_value = "***" if (hide_sensitive and self.api_key) else self.api_key

        return {
            "provider_type": self.provider_type,
            "model": self.model,
            "api_key": api_key_value,
            "base_url": self.base_url,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "timeout": self.timeout,
            "extra_params": self.extra_params
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LLMConfig':
        """Create LLMConfig from dictionary"""
        # Filter out any keys that aren't valid for the dataclass
        valid_fields = {f.name for f in cls.__dataclass_fields__.values()}
        filtered_data = {k: v for k, v in data.items() if k in valid_fields}

        return cls(**filtered_data)

    def copy_with_overrides(self, **overrides) -> 'LLMConfig':
        """Create a copy of the config with overrides"""
        current_dict = self.to_dict(hide_sensitive=False)
        current_dict.update(overrides)
        return self.from_dict(current_dict)


class LLMConfigManager:
    """Manager for LLM configurations with file persistence and environment integration"""

    def __init__(self, config_file: Optional[Union[str, Path]] = None):
        self.config_file = Path(config_file) if config_file else self._get_default_config_path()
        self._ensure_config_dir()

    def _get_default_config_path(self) -> Path:
        """Get default configuration file path"""
        config_dir = Path.home() / ".blog_workflow"
        return config_dir / "llm_config.json"

    def _ensure_config_dir(self) -> None:
        """Ensure configuration directory exists"""
        try:
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
        except OSError as e:
            raise ConfigError(f"Failed to create config directory: {e}")

    def _get_api_key_from_env(self, provider_type: str) -> Optional[str]:
        """Get API key from environment variables"""
        env_var_mapping = {
            ProviderType.OPENAI.value: "OPENAI_API_KEY",
            ProviderType.ANTHROPIC.value: "ANTHROPIC_API_KEY",
            # Ollama and Simulator don't typically need API keys
            ProviderType.OLLAMA.value: "OLLAMA_API_KEY",  # Optional
            ProviderType.SIMULATOR.value: None
        }

        env_var = env_var_mapping.get(provider_type)
        if env_var:
            api_key = os.getenv(env_var)
            if api_key:
                logger.debug(f"Found API key in environment variable: {env_var}")
            return api_key
        return None

    def _get_config_from_env(self, provider_type: str) -> Dict[str, Any]:
        """Get complete configuration from environment variables"""
        config = {}

        # Get API key
        api_key = self._get_api_key_from_env(provider_type)
        if api_key:
            config["api_key"] = api_key

        # Provider-specific environment variables
        if provider_type == ProviderType.OPENAI.value:
            config.update({
                "model": os.getenv("OPENAI_MODEL", "gpt-4"),
                "base_url": os.getenv("OPENAI_BASE_URL"),
                "temperature": float(os.getenv("OPENAI_TEMPERATURE", "0.7")),
                "max_tokens": int(os.getenv("OPENAI_MAX_TOKENS", "4000")),
                "timeout": int(os.getenv("OPENAI_TIMEOUT", "60"))
            })
        elif provider_type == ProviderType.ANTHROPIC.value:
            config.update({
                "model": os.getenv("ANTHROPIC_MODEL", "claude-3-sonnet-20240229"),
                "base_url": os.getenv("ANTHROPIC_BASE_URL"),
                "temperature": float(os.getenv("ANTHROPIC_TEMPERATURE", "0.7")),
                "max_tokens": int(os.getenv("ANTHROPIC_MAX_TOKENS", "4000")),
                "timeout": int(os.getenv("ANTHROPIC_TIMEOUT", "60"))
            })
        elif provider_type == ProviderType.OLLAMA.value:
            config.update({
                "model": os.getenv("OLLAMA_MODEL", "llama2"),
                "base_url": os.getenv("OLLAMA_BASE_URL", "http://localhost:11434"),
                "temperature": float(os.getenv("OLLAMA_TEMPERATURE", "0.7")),
                "max_tokens": int(os.getenv("OLLAMA_MAX_TOKENS", "4000")),
                "timeout": int(os.getenv("OLLAMA_TIMEOUT", "120"))
            })

        # Remove None values
        return {k: v for k, v in config.items() if v is not None}

    def load_config(self) -> LLMConfig:
        """Load configuration from file with environment fallbacks"""
        try:
            config_data = self._load_config_file()
        except FileNotFoundError:
            logger.info(f"Config file not found at {self.config_file}, using environment and defaults")
            config_data = {}
        except json.JSONDecodeError as e:
            raise ConfigError(f"Invalid JSON in config file {self.config_file}: {e}")
        except Exception as e:
            raise ConfigError(f"Failed to load config: {e}")

        # Determine provider type (from file, env, or default)
        provider_type = (
            config_data.get("provider_type") or
            os.getenv("LLM_PROVIDER") or
            ProviderType.SIMULATOR.value
        )

        # Get environment configuration for this provider
        env_config = self._get_config_from_env(provider_type)

        # Merge configurations: file config takes precedence over env config
        merged_config = {**env_config, **config_data}
        merged_config["provider_type"] = provider_type

        try:
            return LLMConfig.from_dict(merged_config)
        except Exception as e:
            logger.warning(f"Failed to create config from merged data: {e}")
            return self._create_default_config()

    def _load_config_file(self) -> Dict[str, Any]:
        """Load raw configuration data from file"""
        with open(self.config_file, 'r', encoding='utf-8') as f:
            return json.load(f)

    def _create_default_config(self) -> LLMConfig:
        """Create default configuration with environment API keys if available"""
        default_provider = ProviderType.SIMULATOR.value
        api_key = self._get_api_key_from_env(default_provider)

        return LLMConfig(
            provider_type=default_provider,
            api_key=api_key
        )

    def save_config(self, config: LLMConfig) -> None:
        """Save configuration to file"""
        try:
            config_dict = config.to_dict(hide_sensitive=False)

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)

            logger.info(f"Configuration saved to {self.config_file}")

        except OSError as e:
            raise ConfigError(f"Failed to save config to {self.config_file}: {e}")
        except Exception as e:
            raise ConfigError(f"Unexpected error saving config: {e}")

    def update_config(self, **updates) -> LLMConfig:
        """Update existing configuration with new values"""
        current_config = self.load_config()
        updated_config = current_config.copy_with_overrides(**updates)
        self.save_config(updated_config)
        return updated_config

    def get_available_providers(self) -> List[str]:
        """Get list of available provider types"""
        try:
            return get_available_providers()
        except Exception as e:
            logger.warning(f"Failed to get available providers: {e}")
            return [p.value for p in ProviderType]

    def validate_provider_connection(self, config: LLMConfig) -> bool:
        """Validate that the provider configuration works"""
        # This would need to be implemented based on your provider interface
        # For now, just validate that required fields are present
        try:
            provider_requires_api_key = config.provider_type in [
                ProviderType.OPENAI.value,
                ProviderType.ANTHROPIC.value
            ]

            if provider_requires_api_key and not config.api_key:
                logger.error(f"Provider {config.provider_type} requires an API key")
                return False

            # Additional validation could be added here
            return True

        except Exception as e:
            logger.error(f"Provider validation failed: {e}")
            return False

    def reset_to_defaults(self) -> LLMConfig:
        """Reset configuration to defaults"""
        default_config = self._create_default_config()
        self.save_config(default_config)
        return default_config

    def backup_config(self, backup_suffix: str = ".backup") -> Path:
        """Create a backup of the current configuration"""
        if not self.config_file.exists():
            raise ConfigError("No configuration file to backup")

        backup_path = self.config_file.with_suffix(self.config_file.suffix + backup_suffix)

        try:
            backup_path.write_text(self.config_file.read_text(encoding='utf-8'), encoding='utf-8')
            logger.info(f"Configuration backed up to {backup_path}")
            return backup_path
        except OSError as e:
            raise ConfigError(f"Failed to create backup: {e}")


# Utility functions for common configuration tasks
def create_config_from_env(provider_type: str = None) -> LLMConfig:
    """Create configuration primarily from environment variables"""
    if not provider_type:
        # Try to detect provider from LLM_PROVIDER env var or available API keys
        provider_type = os.getenv("LLM_PROVIDER")

        if not provider_type:
            if os.getenv("OPENAI_API_KEY"):
                provider_type = ProviderType.OPENAI.value
            elif os.getenv("ANTHROPIC_API_KEY"):
                provider_type = ProviderType.ANTHROPIC.value
            else:
                provider_type = ProviderType.SIMULATOR.value

    manager = LLMConfigManager()
    env_config = manager._get_config_from_env(provider_type)
    env_config["provider_type"] = provider_type

    return LLMConfig.from_dict(env_config)


def get_config_summary(config: LLMConfig) -> str:
    """Get a human-readable summary of the configuration"""
    has_api_key = "Yes" if config.api_key else "No"

    return f"""
LLM Configuration Summary:
  Provider: {config.provider_type}
  Model: {config.model}
  Temperature: {config.temperature}
  Max Tokens: {config.max_tokens}
  Timeout: {config.timeout}s
  API Key Set: {has_api_key}
  Base URL: {config.base_url or 'Default'}
  Extra Parameters: {len(config.extra_params)} items
    """.strip()


def setup_llm_interactively() -> LLMConfig:
    """Interactive setup for LLM configuration"""
    print("\n🔧 Interactive LLM Configuration Setup")
    print("=" * 40)

    try:
        from ..integrations.llm_provider import ProviderType, get_available_providers

        # Get available providers
        available = get_available_providers()

        print("\nAvailable providers:")
        for i, provider in enumerate(available, 1):
            print(f"{i}. {provider}")

        # Provider selection
        while True:
            try:
                choice = input(f"\nSelect provider (1-{len(available)}) [1]: ").strip() or "1"
                provider_index = int(choice) - 1
                if 0 <= provider_index < len(available):
                    provider_type = available[provider_index]
                    break
                else:
                    print("Invalid choice. Please try again.")
            except ValueError:
                print("Please enter a valid number.")

        print(f"\nSelected provider: {provider_type}")

        # Create base config
        config = LLMConfig(provider_type=provider_type)

        # API key setup for providers that need it
        if provider_type in [ProviderType.OPENAI.value, ProviderType.ANTHROPIC.value]:
            env_key = {
                ProviderType.OPENAI.value: "OPENAI_API_KEY",
                ProviderType.ANTHROPIC.value: "ANTHROPIC_API_KEY"
            }.get(provider_type)

            current_key = os.getenv(env_key) if env_key else None

            if current_key:
                use_env = input(f"Use API key from {env_key}? (y/n) [y]: ").strip().lower()
                if use_env in ['', 'y', 'yes']:
                    config.api_key = current_key
                    print("✅ Using API key from environment")
                else:
                    api_key = input("Enter API key: ").strip()
                    if api_key:
                        config.api_key = api_key
            else:
                api_key = input("Enter API key: ").strip()
                if api_key:
                    config.api_key = api_key
                else:
                    print("⚠️ No API key provided. This may cause authentication errors.")

        # Model selection
        default_model = config.model
        model = input(f"Model [{default_model}]: ").strip() or default_model
        config.model = model

        # Advanced settings
        advanced = input("Configure advanced settings? (y/n) [n]: ").strip().lower()
        if advanced in ['y', 'yes']:
            # Temperature
            temp_input = input(f"Temperature (0.0-2.0) [{config.temperature}]: ").strip()
            if temp_input:
                try:
                    config.temperature = float(temp_input)
                except ValueError:
                    print("Invalid temperature, using default")

            # Max tokens
            tokens_input = input(f"Max tokens [{config.max_tokens}]: ").strip()
            if tokens_input:
                try:
                    config.max_tokens = int(tokens_input)
                except ValueError:
                    print("Invalid max tokens, using default")

        # Save configuration
        save_config = input("Save configuration? (y/n) [y]: ").strip().lower()
        if save_config in ['', 'y', 'yes']:
            manager = LLMConfigManager()
            manager.save_config(config)
            print("✅ Configuration saved")

        print("\n" + get_config_summary(config))
        return config

    except KeyboardInterrupt:
        print("\n⏹️ Setup cancelled")
        return LLMConfig()  # Return default config
    except Exception as e:
        print(f"❌ Setup error: {e}")
        return LLMConfig()  # Return default config