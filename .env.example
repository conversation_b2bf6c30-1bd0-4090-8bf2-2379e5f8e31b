# ===================================================================
# Blog Workflow LangGraph - Environment Configuration
# ===================================================================
# Copy this file to .env and configure your settings
# cp .env.example .env

# ===================================================================
# LLM PROVIDERS CONFIGURATION
# ===================================================================

# OpenAI Configuration
OPENAI_API_KEY=sk-your-openai-key-here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.7

# Anthropic Configuration
ANTHROPIC_API_KEY=sk-ant-your-anthropic-key-here
ANTHROPIC_MODEL=claude-3-sonnet-20240229
ANTHROPIC_MAX_TOKENS=4000
ANTHROPIC_TEMPERATURE=0.7

# Ollama Configuration (Local LLM)
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2
OLLAMA_TIMEOUT=120

# Default LLM Provider
# Options: openai, anthropic, ollama, simulator
LLM_PROVIDER=simulator

# ===================================================================
# WORKFLOW CONFIGURATION
# ===================================================================

# Workflow Settings
MAX_REVISIONS=3
CONTENT_PREVIEW_LENGTH=500
ENABLE_HUMAN_REVIEW=true
AUTO_SEO_OPTIMIZATION=true
WORKFLOW_TIMEOUT=300

# Content Generation Settings
MIN_CONTENT_LENGTH=1000
MAX_CONTENT_LENGTH=5000
TARGET_READING_TIME=5

# ===================================================================
# API & APPLICATION CONFIGURATION
# ===================================================================

# API Settings
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=true
API_BASE_URL=http://localhost:8000

# Streamlit Settings
STREAMLIT_HOST=0.0.0.0
STREAMLIT_PORT=8501

# ===================================================================
# DATABASE & STORAGE
# ===================================================================

# Database Configuration
DATABASE_PATH=./data/workflow.db
DATABASE_ECHO=false

# File Storage
DATA_DIR=./data
LOGS_DIR=./logs
EXPORTS_DIR=./exports

# ===================================================================
# LOGGING & MONITORING
# ===================================================================

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_FILE=./logs/workflow.log
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090

# ===================================================================
# SECURITY & PERFORMANCE
# ===================================================================

# Security
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1
CORS_ORIGINS=http://localhost:3000,http://localhost:8501

# Performance
WORKER_PROCESSES=1
WORKER_CONNECTIONS=1000
KEEPALIVE_TIMEOUT=5

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# ===================================================================
# DEVELOPMENT & TESTING
# ===================================================================

# Development Mode
DEBUG=false
DEVELOPMENT_MODE=false

# Testing
TEST_DATABASE_PATH=./data/test_workflow.db
MOCK_LLM_RESPONSES=false

# ===================================================================
# EXTERNAL INTEGRATIONS
# ===================================================================

# Optional: External APIs for enhanced functionality
SERP_API_KEY=your-serp-api-key-here
UNSPLASH_ACCESS_KEY=your-unsplash-key-here

# Optional: Analytics
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
MIXPANEL_TOKEN=your-mixpanel-token-here

# ===================================================================
# DOCKER & DEPLOYMENT
# ===================================================================

# Docker Configuration
DOCKER_REGISTRY=your-registry.com
DOCKER_IMAGE_TAG=latest

# Deployment
ENVIRONMENT=development
DEPLOY_REGION=us-east-1
