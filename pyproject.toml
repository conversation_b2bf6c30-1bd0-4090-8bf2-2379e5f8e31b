[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "blog-workflow-langgraph"
version = "1.0.0"
description = "Sistema avançado para criação de conteúdo de blogs com LangGraph e revisão humana"
readme = "README.md"
authors = [{ name = "<PERSON>", email = "<EMAIL>" }]
keywords = [
  "blog",
  "content-creation",
  "workflow",
  "langgraph",
  "seo",
  "automation",
  "ai-writing",
  "llm",
  "portfolio",
]
classifiers = [
  "Development Status :: 4 - Beta",
  "Intended Audience :: Developers",
  "License :: OSI Approved :: MIT License",
  "Operating System :: OS Independent",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Topic :: Software Development :: Libraries :: Python Modules",
  "Topic :: Text Processing :: Linguistic",
  "Topic :: Internet :: WWW/HTTP :: Dynamic Content",
]
requires-python = ">=3.8"
dependencies = [
  "pydantic>=2.0.0",
  "python-dateutil>=2.8.0",
  "typing-extensions>=4.0.0; python_version<'3.10'",
  "requests>=2.28.0",
  "python-dotenv>=1.0.0",
]

[project.optional-dependencies]
dev = [
  "pytest>=7.0.0",
  "pytest-cov>=4.0.0",
  "pytest-asyncio>=0.21.0",
  "pytest-mock>=3.11.0",
  "black>=23.0.0",
  "isort>=5.12.0",
  "mypy>=1.5.0",
  "flake8>=6.0.0",
  "bandit>=1.7.0",
  "safety>=2.3.0",
  "pre-commit>=3.0.0",
]
llm = [
  "openai>=1.0.0",
  "anthropic>=0.3.0",
  "langgraph>=0.1.0",
  "langchain>=0.1.0",
  "tiktoken>=0.5.0",
]
api = [
  "fastapi>=0.100.0",
  "uvicorn[standard]>=0.23.0",
  "pydantic-settings>=2.0.0",
  "python-multipart>=0.0.6",
]
ui = ["streamlit>=1.28.0", "plotly>=5.15.0", "altair>=5.0.0"]
monitoring = ["prometheus-client>=0.17.0", "structlog>=23.0.0"]
all = ["blog-workflow-langgraph[dev,llm,api,ui,monitoring]"]

[project.urls]
Homepage = "https://github.com/gabriel/blog-workflow-langgraph"
Repository = "https://github.com/gabriel/blog-workflow-langgraph.git"

[project.scripts]
blog-workflow = "examples.basic_usage:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["src*", "examples*"]
exclude = ["tests*", "docs*", "build*", "dist*"]

[tool.setuptools.package-dir]
"" = "."

[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # Directories
  \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | venv
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src", "examples"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
show_error_codes = true
namespace_packages = true

[[tool.mypy.overrides]]
module = ["langgraph.*", "langchain.*"]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = ["--strict-markers", "--strict-config", "--verbose", "--tb=short"]
markers = [
  "unit: Unit tests",
  "integration: Integration tests",
  "slow: Slow tests",
  "workflow: Workflow-specific tests",
]

[tool.coverage.run]
source = ["src"]
omit = [
  "*/tests/*",
  "*/test_*",
  "*/__pycache__/*",
  "*/venv/*",
  "*/build/*",
  "*/dist/*",
]

[tool.coverage.report]
exclude_lines = [
  "pragma: no cover",
  "def __repr__",
  "raise AssertionError",
  "raise NotImplementedError",
  "if __name__ == .__main__.:",
  "if TYPE_CHECKING:",
]
show_missing = true
precision = 2

[tool.flake8]
max-line-length = 88
extend-ignore = [
  "E203", # whitespace before ':'
  "E501", # line too long (handled by black)
  "W503", # line break before binary operator
]
exclude = [
  ".git",
  "__pycache__",
  "build",
  "dist",
  "venv",
  ".venv",
  ".mypy_cache",
  ".pytest_cache",
]
