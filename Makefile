.PHONY: help install install-dev test lint format clean docker-build docker-run docker-stop setup demo

# Default target
help: ## Show this help message
	@echo "🚀 Blog Workflow LangGraph - Available Commands:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

# Quick Setup
setup: ## Complete project setup (recommended for first use)
	python setup_project.py

# Installation
install: ## Install the package
	pip install -e .

install-dev: ## Install with development dependencies
	pip install -e ".[dev]"

install-all: ## Install with all optional dependencies
	pip install -e ".[dev,enhanced]"
	pip install fastapi uvicorn streamlit

# Environment Setup
setup-env: ## Interactive environment setup (.env file)
	python scripts/setup_env.py

create-env: ## Create .env from .env.example
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "✅ Created .env from .env.example"; \
		echo "📝 Please edit .env with your API keys and settings"; \
	else \
		echo "⚠️ .env already exists. Use 'make setup-env' to update it."; \
	fi

check-env: ## Check .env configuration
	@echo "🔍 Checking environment configuration..."
	@python -c "from src.config.llm_config import create_config_from_env; config = create_config_from_env(); print('✅ Environment configuration loaded successfully'); print(f'Provider: {config.provider_type}'); print(f'Model: {config.model}'); print(f'API Key: {\"Set\" if config.api_key else \"Not set\"}')"

# LLM Setup (Legacy - use setup-env instead)
setup-openai: ## Setup OpenAI integration (use setup-env instead)
	@echo "📋 Setting up OpenAI integration..."
	@echo "💡 Tip: Use 'make setup-env' for interactive setup"
	@echo "Add to .env: OPENAI_API_KEY=sk-your-key-here"

setup-anthropic: ## Setup Anthropic integration (use setup-env instead)
	@echo "📋 Setting up Anthropic integration..."
	@echo "💡 Tip: Use 'make setup-env' for interactive setup"
	@echo "Add to .env: ANTHROPIC_API_KEY=sk-ant-your-key-here"

# Testing and Quality
test: ## Run basic tests
	python test_simple.py

test-services: ## Test API and Streamlit services
	python scripts/test_services.py

test-full: ## Run full test suite
	pytest tests/ -v

test-cov: ## Run tests with coverage
	pytest tests/ --cov=src --cov-report=html

test-env: ## Test environment configuration
	python scripts/test_services.py

lint: ## Run code linting
	black --check src/ examples/ tests/
	isort --check-only src/ examples/ tests/

format: ## Format code
	black src/ examples/ tests/
	isort src/ examples/ tests/

# Development
clean: ## Clean build artifacts
	rm -rf build/ dist/ *.egg-info/ .pytest_cache/ .coverage htmlcov/
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

# Docker commands
docker-build: ## Build Docker image
	docker-compose build

docker-build-api: ## Build API Docker image
	docker build -f Dockerfile.api -t blog-workflow-api:latest .

docker-build-ui: ## Build Streamlit Docker image
	docker build -f Dockerfile.streamlit -t blog-workflow-ui:latest .

docker-run: ## Run with Docker Compose
	docker-compose up -d

docker-run-dev: ## Run in development mode
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

docker-stop: ## Stop Docker containers
	docker-compose down

docker-logs: ## View Docker logs
	docker-compose logs -f

docker-clean: ## Clean Docker images and containers
	docker-compose down -v --remove-orphans
	docker system prune -f

# Demos
demo: ## Run interactive demo
	python examples/basic_usage.py

demo-simple: ## Run simple demo (no interaction)
	echo "n" | python examples/basic_usage.py

# API & UI
run-api: ## Run FastAPI server
	uvicorn api.main:app --reload --host 0.0.0.0 --port 8000

run-ui: ## Run Streamlit app
	streamlit run apps/streamlit_app.py

# Quick commands
quickstart: setup create-env demo ## Complete setup and demo

quickstart-full: setup setup-env demo ## Complete setup with interactive env configuration
