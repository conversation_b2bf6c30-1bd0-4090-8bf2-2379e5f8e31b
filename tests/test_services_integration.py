"""
Integration tests for API and Streamlit services
"""

import pytest
import requests
import time
import subprocess
import signal
import os
from pathlib import Path


class TestServicesIntegration:
    """Test integration between API and Streamlit services"""
    
    @pytest.fixture(scope="class")
    def api_server(self):
        """Start API server for testing"""
        project_root = Path(__file__).parent.parent
        
        # Start API server
        process = subprocess.Popen([
            "python", "-m", "uvicorn", "api.main:app",
            "--host", "127.0.0.1",
            "--port", "8000"
        ], cwd=project_root)
        
        # Wait for server to start
        max_attempts = 30
        for _ in range(max_attempts):
            try:
                response = requests.get("http://127.0.0.1:8000/health", timeout=1)
                if response.status_code == 200:
                    break
            except:
                time.sleep(1)
        else:
            process.terminate()
            pytest.fail("API server failed to start")
        
        yield process
        
        # Cleanup
        process.terminate()
        process.wait()

    def test_api_health_endpoint(self, api_server):
        """Test API health endpoint"""
        response = requests.get("http://127.0.0.1:8000/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "version" in data

    def test_api_create_workflow(self, api_server):
        """Test creating a workflow via API"""
        payload = {"topic": "Test Topic for API"}
        response = requests.post(
            "http://127.0.0.1:8000/workflows",
            json=payload
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "thread_id" in data
        assert data["topic"] == "Test Topic for API"
        assert data["status"] in ["finalizer", "completed"]
        
        return data["thread_id"]

    def test_api_get_workflow_status(self, api_server):
        """Test getting workflow status"""
        # First create a workflow
        payload = {"topic": "Status Test Topic"}
        create_response = requests.post(
            "http://127.0.0.1:8000/workflows",
            json=payload
        )
        thread_id = create_response.json()["thread_id"]
        
        # Get status
        response = requests.get(f"http://127.0.0.1:8000/workflows/{thread_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["thread_id"] == thread_id
        assert data["topic"] == "Status Test Topic"
        assert "status" in data

    def test_api_get_final_post(self, api_server):
        """Test getting final post content"""
        # Create workflow
        payload = {"topic": "Final Post Test"}
        create_response = requests.post(
            "http://127.0.0.1:8000/workflows",
            json=payload
        )
        thread_id = create_response.json()["thread_id"]
        
        # Get final post
        response = requests.get(f"http://127.0.0.1:8000/workflows/{thread_id}/final")
        
        assert response.status_code == 200
        data = response.json()
        assert data["thread_id"] == thread_id
        assert "final_post" in data
        assert len(data["final_post"]) > 0

    def test_api_list_workflows(self, api_server):
        """Test listing workflows"""
        # Create a workflow first
        payload = {"topic": "List Test Topic"}
        requests.post("http://127.0.0.1:8000/workflows", json=payload)
        
        # List workflows
        response = requests.get("http://127.0.0.1:8000/workflows")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    def test_api_error_handling(self, api_server):
        """Test API error handling"""
        # Test missing topic
        response = requests.post("http://127.0.0.1:8000/workflows", json={})
        assert response.status_code == 400
        
        # Test non-existent workflow
        response = requests.get("http://127.0.0.1:8000/workflows/non-existent")
        assert response.status_code == 404

    def test_streamlit_imports(self):
        """Test that Streamlit app imports work correctly"""
        try:
            from apps.streamlit_app import (
                make_api_request,
                create_workflow,
                get_workflow_status,
                submit_review
            )
            # If we get here, imports are working
            assert True
        except ImportError as e:
            pytest.fail(f"Streamlit app import failed: {e}")

    def test_streamlit_api_integration_functions(self):
        """Test Streamlit API integration functions"""
        from apps.streamlit_app import make_api_request
        
        # Test with a mock endpoint (this will fail but we're testing the function structure)
        success, response = make_api_request("/non-existent-endpoint")
        assert isinstance(success, bool)
        assert isinstance(response, dict)
        assert not success  # Should fail for non-existent endpoint
        assert "error" in response

    def test_environment_configuration(self):
        """Test environment configuration loading"""
        from src.config.llm_config import create_config_from_env
        
        # Should work with default simulator
        config = create_config_from_env()
        assert config.provider_type == "simulator"

    def test_workflow_manager_initialization(self):
        """Test workflow manager initialization"""
        from src.blog_workflow import BlogWorkflowManager, WorkflowConfig
        
        config = WorkflowConfig(memory_path=":memory:")
        manager = BlogWorkflowManager(config)
        
        # Should initialize without errors
        assert manager is not None
        assert manager.config.memory_path == ":memory:"

    def test_api_cors_headers(self, api_server):
        """Test CORS headers are present"""
        response = requests.options("http://127.0.0.1:8000/health")
        
        # Should have CORS headers
        assert "access-control-allow-origin" in response.headers

    def test_api_documentation_endpoints(self, api_server):
        """Test API documentation endpoints"""
        # Test OpenAPI docs
        response = requests.get("http://127.0.0.1:8000/docs")
        assert response.status_code == 200
        
        # Test OpenAPI JSON
        response = requests.get("http://127.0.0.1:8000/openapi.json")
        assert response.status_code == 200
        data = response.json()
        assert "openapi" in data
        assert "info" in data

    def test_workflow_end_to_end(self, api_server):
        """Test complete workflow end-to-end"""
        # 1. Create workflow
        payload = {"topic": "End-to-End Test Topic"}
        create_response = requests.post(
            "http://127.0.0.1:8000/workflows",
            json=payload
        )
        assert create_response.status_code == 200
        thread_id = create_response.json()["thread_id"]
        
        # 2. Check status
        status_response = requests.get(f"http://127.0.0.1:8000/workflows/{thread_id}")
        assert status_response.status_code == 200
        
        # 3. Get final post (should be available since we use simulator)
        final_response = requests.get(f"http://127.0.0.1:8000/workflows/{thread_id}/final")
        assert final_response.status_code == 200
        final_data = final_response.json()
        assert len(final_data["final_post"]) > 0
        
        # 4. Get SEO suggestions
        seo_response = requests.get(f"http://127.0.0.1:8000/workflows/{thread_id}/seo")
        assert seo_response.status_code == 200

    def test_api_performance(self, api_server):
        """Test API performance"""
        import time
        
        start_time = time.time()
        response = requests.get("http://127.0.0.1:8000/health")
        end_time = time.time()
        
        assert response.status_code == 200
        # Health endpoint should respond quickly (under 1 second)
        assert (end_time - start_time) < 1.0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
