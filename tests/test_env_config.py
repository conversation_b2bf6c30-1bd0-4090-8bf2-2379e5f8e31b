"""
Tests for environment-based configuration system
"""

import os
import tempfile
import pytest
from pathlib import Path
from unittest.mock import patch

from src.config.llm_config import (
    LLMConfig,
    LLMConfigManager,
    create_config_from_env,
    ConfigError
)
from src.integrations.llm_provider import ProviderType


class TestEnvironmentConfiguration:
    """Test environment-based configuration loading"""

    def test_load_openai_config_from_env(self):
        """Test loading OpenAI configuration from environment variables"""
        env_vars = {
            "LLM_PROVIDER": "openai",
            "OPENAI_API_KEY": "sk-test-key",
            "OPENAI_MODEL": "gpt-3.5-turbo",
            "OPENAI_TEMPERATURE": "0.8",
            "OPENAI_MAX_TOKENS": "2000",
            "OPENAI_TIMEOUT": "30"
        }
        
        with patch.dict(os.environ, env_vars, clear=False):
            config = create_config_from_env()
            
            assert config.provider_type == ProviderType.OPENAI.value
            assert config.api_key == "sk-test-key"
            assert config.model == "gpt-3.5-turbo"
            assert config.temperature == 0.8
            assert config.max_tokens == 2000
            assert config.timeout == 30

    def test_load_anthropic_config_from_env(self):
        """Test loading Anthropic configuration from environment variables"""
        env_vars = {
            "LLM_PROVIDER": "anthropic",
            "ANTHROPIC_API_KEY": "sk-ant-test-key",
            "ANTHROPIC_MODEL": "claude-3-haiku-20240307",
            "ANTHROPIC_TEMPERATURE": "0.5",
            "ANTHROPIC_MAX_TOKENS": "1500",
            "ANTHROPIC_TIMEOUT": "45"
        }
        
        with patch.dict(os.environ, env_vars, clear=False):
            config = create_config_from_env()
            
            assert config.provider_type == ProviderType.ANTHROPIC.value
            assert config.api_key == "sk-ant-test-key"
            assert config.model == "claude-3-haiku-20240307"
            assert config.temperature == 0.5
            assert config.max_tokens == 1500
            assert config.timeout == 45

    def test_load_ollama_config_from_env(self):
        """Test loading Ollama configuration from environment variables"""
        env_vars = {
            "LLM_PROVIDER": "ollama",
            "OLLAMA_BASE_URL": "http://localhost:11434",
            "OLLAMA_MODEL": "llama2:13b",
            "OLLAMA_TEMPERATURE": "0.9",
            "OLLAMA_MAX_TOKENS": "3000",
            "OLLAMA_TIMEOUT": "180"
        }
        
        with patch.dict(os.environ, env_vars, clear=False):
            config = create_config_from_env()
            
            assert config.provider_type == ProviderType.OLLAMA.value
            assert config.model == "llama2:13b"
            assert config.base_url == "http://localhost:11434"
            assert config.temperature == 0.9
            assert config.max_tokens == 3000
            assert config.timeout == 180

    def test_auto_detect_provider_from_api_keys(self):
        """Test automatic provider detection from available API keys"""
        # Test OpenAI detection
        with patch.dict(os.environ, {"OPENAI_API_KEY": "sk-test"}, clear=True):
            config = create_config_from_env()
            assert config.provider_type == ProviderType.OPENAI.value
            assert config.api_key == "sk-test"
        
        # Test Anthropic detection
        with patch.dict(os.environ, {"ANTHROPIC_API_KEY": "sk-ant-test"}, clear=True):
            config = create_config_from_env()
            assert config.provider_type == ProviderType.ANTHROPIC.value
            assert config.api_key == "sk-ant-test"
        
        # Test fallback to simulator
        with patch.dict(os.environ, {}, clear=True):
            config = create_config_from_env()
            assert config.provider_type == ProviderType.SIMULATOR.value

    def test_config_manager_env_integration(self):
        """Test LLMConfigManager integration with environment variables"""
        env_vars = {
            "LLM_PROVIDER": "openai",
            "OPENAI_API_KEY": "sk-manager-test",
            "OPENAI_MODEL": "gpt-4",
            "MAX_REVISIONS": "5",
            "LOG_LEVEL": "DEBUG"
        }
        
        with patch.dict(os.environ, env_vars, clear=False):
            with tempfile.TemporaryDirectory() as temp_dir:
                config_file = Path(temp_dir) / "test_config.json"
                manager = LLMConfigManager(config_file)
                
                # Load config (should use env vars since file doesn't exist)
                config = manager.load_config()
                
                assert config.provider_type == ProviderType.OPENAI.value
                assert config.api_key == "sk-manager-test"
                assert config.model == "gpt-4"

    def test_file_config_overrides_env(self):
        """Test that file configuration takes precedence over environment"""
        env_vars = {
            "LLM_PROVIDER": "openai",
            "OPENAI_API_KEY": "sk-env-key",
            "OPENAI_MODEL": "gpt-3.5-turbo"
        }
        
        file_config = {
            "provider_type": "anthropic",
            "api_key": "sk-file-key",
            "model": "claude-3-sonnet-20240229"
        }
        
        with patch.dict(os.environ, env_vars, clear=False):
            with tempfile.TemporaryDirectory() as temp_dir:
                config_file = Path(temp_dir) / "test_config.json"
                
                # Write file config
                import json
                with open(config_file, 'w') as f:
                    json.dump(file_config, f)
                
                manager = LLMConfigManager(config_file)
                config = manager.load_config()
                
                # File config should override env
                assert config.provider_type == "anthropic"
                assert config.api_key == "sk-file-key"
                assert config.model == "claude-3-sonnet-20240229"

    def test_partial_env_config(self):
        """Test configuration with only some environment variables set"""
        env_vars = {
            "OPENAI_API_KEY": "sk-partial-test",
            "OPENAI_TEMPERATURE": "0.3"
            # Missing model, max_tokens, etc.
        }
        
        with patch.dict(os.environ, env_vars, clear=False):
            config = create_config_from_env("openai")
            
            assert config.provider_type == ProviderType.OPENAI.value
            assert config.api_key == "sk-partial-test"
            assert config.temperature == 0.3
            # Should use defaults for missing values
            assert config.model == "gpt-4"  # Default model
            assert config.max_tokens == 4000  # Default max_tokens

    def test_invalid_env_values_fallback(self):
        """Test handling of invalid environment variable values"""
        env_vars = {
            "LLM_PROVIDER": "openai",
            "OPENAI_API_KEY": "sk-test",
            "OPENAI_TEMPERATURE": "invalid_float",
            "OPENAI_MAX_TOKENS": "invalid_int"
        }
        
        with patch.dict(os.environ, env_vars, clear=False):
            # Should handle invalid values gracefully
            try:
                config = create_config_from_env()
                # If it doesn't raise an exception, check defaults are used
                assert config.provider_type == ProviderType.OPENAI.value
                assert config.api_key == "sk-test"
            except (ValueError, ConfigError):
                # It's acceptable to raise an error for invalid values
                pass

    def test_env_config_validation(self):
        """Test that environment-loaded config still passes validation"""
        env_vars = {
            "LLM_PROVIDER": "openai",
            "OPENAI_API_KEY": "sk-validation-test",
            "OPENAI_TEMPERATURE": "1.5",  # Valid range
            "OPENAI_MAX_TOKENS": "2000",  # Valid range
            "OPENAI_TIMEOUT": "60"  # Valid range
        }
        
        with patch.dict(os.environ, env_vars, clear=False):
            config = create_config_from_env()
            
            # Should pass all validations
            assert 0.0 <= config.temperature <= 2.0
            assert 1 <= config.max_tokens <= 100000
            assert 1 <= config.timeout <= 300

    def test_dotenv_loading_simulation(self):
        """Test that dotenv loading works when available"""
        # This test simulates the dotenv loading behavior
        # In real usage, dotenv would load from .env file
        
        env_vars = {
            "OPENAI_API_KEY": "sk-dotenv-test",
            "LLM_PROVIDER": "openai"
        }
        
        with patch.dict(os.environ, env_vars, clear=False):
            # Import should trigger dotenv loading
            from src.config.llm_config import create_config_from_env
            
            config = create_config_from_env()
            assert config.api_key == "sk-dotenv-test"
            assert config.provider_type == ProviderType.OPENAI.value

    def test_workflow_config_from_env(self):
        """Test loading workflow-specific configuration from environment"""
        env_vars = {
            "MAX_REVISIONS": "5",
            "CONTENT_PREVIEW_LENGTH": "1000",
            "ENABLE_HUMAN_REVIEW": "false",
            "AUTO_SEO_OPTIMIZATION": "true"
        }
        
        with patch.dict(os.environ, env_vars, clear=False):
            # Test that workflow config can be loaded from env
            max_revisions = int(os.getenv("MAX_REVISIONS", "3"))
            preview_length = int(os.getenv("CONTENT_PREVIEW_LENGTH", "500"))
            human_review = os.getenv("ENABLE_HUMAN_REVIEW", "true").lower() == "true"
            seo_optimization = os.getenv("AUTO_SEO_OPTIMIZATION", "true").lower() == "true"
            
            assert max_revisions == 5
            assert preview_length == 1000
            assert human_review is False
            assert seo_optimization is True


if __name__ == "__main__":
    pytest.main([__file__])
