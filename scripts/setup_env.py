#!/usr/bin/env python3
"""
🔧 Environment Setup Script for Blog Workflow LangGraph

This script helps you set up your .env file with the necessary configuration
for LLM providers and other system settings.
"""

import os
import sys
from pathlib import Path
from typing import Dict, Optional

def get_project_root() -> Path:
    """Get the project root directory"""
    current_dir = Path(__file__).parent
    # Go up one level from scripts/ to get to project root
    return current_dir.parent

def load_env_example() -> Dict[str, str]:
    """Load the .env.example file to get available variables"""
    project_root = get_project_root()
    env_example_path = project_root / ".env.example"
    
    if not env_example_path.exists():
        print("❌ .env.example file not found!")
        return {}
    
    env_vars = {}
    with open(env_example_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                env_vars[key] = value
    
    return env_vars

def get_existing_env() -> Dict[str, str]:
    """Load existing .env file if it exists"""
    project_root = get_project_root()
    env_path = project_root / ".env"
    
    if not env_path.exists():
        return {}
    
    env_vars = {}
    with open(env_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                env_vars[key] = value
    
    return env_vars

def prompt_for_value(key: str, current_value: str, description: str = "") -> str:
    """Prompt user for a configuration value"""
    if description:
        print(f"\n📝 {description}")
    
    if current_value and current_value != f"your-{key.lower().replace('_', '-')}-here":
        prompt = f"{key} [{current_value}]: "
    else:
        prompt = f"{key}: "
    
    value = input(prompt).strip()
    return value if value else current_value

def setup_llm_providers() -> Dict[str, str]:
    """Interactive setup for LLM providers"""
    print("\n🤖 LLM Provider Configuration")
    print("=" * 50)
    
    config = {}
    
    # Ask which providers to configure
    print("\nWhich LLM providers would you like to configure?")
    print("1. OpenAI (GPT-4, GPT-3.5)")
    print("2. Anthropic (Claude)")
    print("3. Ollama (Local LLM)")
    print("4. Skip LLM configuration (use simulator)")
    
    choice = input("\nSelect options (comma-separated, e.g., 1,2): ").strip()
    
    if not choice or choice == "4":
        config["LLM_PROVIDER"] = "simulator"
        return config
    
    providers = []
    if "1" in choice:
        providers.append("openai")
    if "2" in choice:
        providers.append("anthropic")
    if "3" in choice:
        providers.append("ollama")
    
    # Set default provider
    if providers:
        config["LLM_PROVIDER"] = providers[0]
    
    # Configure OpenAI
    if "openai" in providers:
        print("\n🔵 OpenAI Configuration")
        print("Get your API key from: https://platform.openai.com/api-keys")
        
        api_key = input("OpenAI API Key: ").strip()
        if api_key:
            config["OPENAI_API_KEY"] = api_key
            config["OPENAI_MODEL"] = input("Model [gpt-4]: ").strip() or "gpt-4"
    
    # Configure Anthropic
    if "anthropic" in providers:
        print("\n🟠 Anthropic Configuration")
        print("Get your API key from: https://console.anthropic.com/")
        
        api_key = input("Anthropic API Key: ").strip()
        if api_key:
            config["ANTHROPIC_API_KEY"] = api_key
            config["ANTHROPIC_MODEL"] = input("Model [claude-3-sonnet-20240229]: ").strip() or "claude-3-sonnet-20240229"
    
    # Configure Ollama
    if "ollama" in providers:
        print("\n🟢 Ollama Configuration")
        print("Make sure Ollama is installed and running: https://ollama.ai/")
        
        base_url = input("Ollama Base URL [http://localhost:11434]: ").strip() or "http://localhost:11434"
        config["OLLAMA_BASE_URL"] = base_url
        config["OLLAMA_MODEL"] = input("Model [llama2]: ").strip() or "llama2"
    
    return config

def setup_basic_config() -> Dict[str, str]:
    """Setup basic workflow configuration"""
    print("\n⚙️ Basic Workflow Configuration")
    print("=" * 50)
    
    config = {}
    
    # Workflow settings
    max_revisions = input("Maximum content revisions [3]: ").strip() or "3"
    config["MAX_REVISIONS"] = max_revisions
    
    # Enable human review
    human_review = input("Enable human review step? (y/n) [y]: ").strip().lower()
    config["ENABLE_HUMAN_REVIEW"] = "true" if human_review in ['', 'y', 'yes'] else "false"
    
    # Log level
    log_level = input("Log level (DEBUG/INFO/WARNING/ERROR) [INFO]: ").strip().upper() or "INFO"
    config["LOG_LEVEL"] = log_level
    
    return config

def write_env_file(config: Dict[str, str]) -> None:
    """Write the configuration to .env file"""
    project_root = get_project_root()
    env_path = project_root / ".env"
    
    # Load existing config
    existing_config = get_existing_env()
    
    # Merge configurations
    final_config = {**existing_config, **config}
    
    # Write to file
    with open(env_path, 'w', encoding='utf-8') as f:
        f.write("# =============================================================================\n")
        f.write("# 🚀 Blog Workflow LangGraph - Environment Configuration\n")
        f.write("# =============================================================================\n")
        f.write("# Generated by setup_env.py script\n")
        f.write("# \n")
        f.write("# IMPORTANT: Never commit this file to version control!\n")
        f.write("# =============================================================================\n\n")
        
        # Write LLM configuration
        f.write("# LLM Provider Configuration\n")
        llm_keys = [k for k in final_config.keys() if any(provider in k for provider in ['OPENAI', 'ANTHROPIC', 'OLLAMA', 'LLM_PROVIDER'])]
        for key in sorted(llm_keys):
            f.write(f"{key}={final_config[key]}\n")
        
        f.write("\n# Workflow Configuration\n")
        workflow_keys = [k for k in final_config.keys() if k.startswith(('MAX_', 'ENABLE_', 'CONTENT_'))]
        for key in sorted(workflow_keys):
            f.write(f"{key}={final_config[key]}\n")
        
        f.write("\n# System Configuration\n")
        system_keys = [k for k in final_config.keys() if k.startswith(('LOG_', 'DEBUG', 'DATABASE_'))]
        for key in sorted(system_keys):
            f.write(f"{key}={final_config[key]}\n")
        
        # Write remaining keys
        remaining_keys = [k for k in final_config.keys() if k not in llm_keys + workflow_keys + system_keys]
        if remaining_keys:
            f.write("\n# Additional Configuration\n")
            for key in sorted(remaining_keys):
                f.write(f"{key}={final_config[key]}\n")
    
    print(f"\n✅ Configuration saved to {env_path}")

def main():
    """Main setup function"""
    print("🚀 Blog Workflow LangGraph - Environment Setup")
    print("=" * 60)
    print("This script will help you configure your .env file.")
    print("You can run this script multiple times to update your configuration.")
    
    try:
        # Check if .env already exists
        project_root = get_project_root()
        env_path = project_root / ".env"
        
        if env_path.exists():
            print(f"\n📁 Found existing .env file at {env_path}")
            overwrite = input("Do you want to update it? (y/n) [y]: ").strip().lower()
            if overwrite in ['n', 'no']:
                print("Setup cancelled.")
                return
        
        # Setup configuration
        config = {}
        
        # LLM providers
        llm_config = setup_llm_providers()
        config.update(llm_config)
        
        # Basic configuration
        basic_config = setup_basic_config()
        config.update(basic_config)
        
        # Write configuration
        write_env_file(config)
        
        print("\n🎉 Environment setup completed!")
        print("\nNext steps:")
        print("1. Review your .env file and adjust settings as needed")
        print("2. Test your configuration with: python test_simple.py")
        print("3. Run the workflow with: make demo")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Setup cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
