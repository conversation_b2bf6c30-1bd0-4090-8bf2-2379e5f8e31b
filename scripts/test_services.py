#!/usr/bin/env python3
"""
🧪 Test Script for Blog Workflow Services

This script tests both the FastAPI and Streamlit services to ensure they're working correctly.
"""

import sys
import time
import requests
import subprocess
import signal
import os
from pathlib import Path
from typing import Optional


def print_status(message: str, status: str = "info"):
    """Print colored status messages"""
    colors = {
        "info": "\033[94m",      # Blue
        "success": "\033[92m",   # Green
        "warning": "\033[93m",   # Yellow
        "error": "\033[91m",     # Red
        "reset": "\033[0m"       # Reset
    }

    icons = {
        "info": "ℹ️",
        "success": "✅",
        "warning": "⚠️",
        "error": "❌"
    }

    color = colors.get(status, colors["info"])
    icon = icons.get(status, "")
    reset = colors["reset"]

    print(f"{color}{icon} {message}{reset}")


def test_imports():
    """Test that all imports work correctly"""
    print_status("Testing imports...", "info")

    # Add current directory to Python path
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))

    try:
        # Test API imports
        from api.main import app
        print_status("API imports: OK", "success")
    except Exception as e:
        print_status(f"API imports failed: {e}", "error")
        return False

    try:
        # Test Streamlit imports (ignore warnings)
        import warnings
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            from apps.streamlit_app import make_api_request
        print_status("Streamlit imports: OK", "success")
    except Exception as e:
        print_status(f"Streamlit imports failed: {e}", "error")
        return False

    try:
        # Test workflow manager
        from src.blog_workflow import BlogWorkflowManager
        manager = BlogWorkflowManager()
        print_status("Workflow manager: OK", "success")
    except Exception as e:
        print_status(f"Workflow manager failed: {e}", "error")
        return False

    try:
        # Test config system
        from src.config.llm_config import create_config_from_env
        config = create_config_from_env()
        print_status(f"Config system: OK (Provider: {config.provider_type})", "success")
    except Exception as e:
        print_status(f"Config system failed: {e}", "error")
        return False

    return True


def start_api_server() -> Optional[subprocess.Popen]:
    """Start the API server"""
    print_status("Starting API server...", "info")

    try:
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", "api.main:app",
            "--host", "127.0.0.1",
            "--port", "8000"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)

        # Wait for server to start
        max_attempts = 30
        for attempt in range(max_attempts):
            try:
                response = requests.get("http://127.0.0.1:8000/health", timeout=1)
                if response.status_code == 200:
                    print_status("API server started successfully", "success")
                    return process
            except:
                time.sleep(1)

        print_status("API server failed to start within 30 seconds", "error")
        process.terminate()
        return None

    except Exception as e:
        print_status(f"Failed to start API server: {e}", "error")
        return None


def test_api_endpoints(base_url: str = "http://127.0.0.1:8000"):
    """Test API endpoints"""
    print_status("Testing API endpoints...", "info")

    # Test health endpoint
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print_status(f"Health endpoint: OK (Status: {data['status']})", "success")
        else:
            print_status(f"Health endpoint failed: {response.status_code}", "error")
            return False
    except Exception as e:
        print_status(f"Health endpoint error: {e}", "error")
        return False

    # Test workflow creation
    try:
        payload = {"topic": "Test API Integration"}
        response = requests.post(f"{base_url}/workflows", json=payload, timeout=30)
        if response.status_code == 200:
            data = response.json()
            thread_id = data["thread_id"]
            print_status(f"Workflow creation: OK (ID: {thread_id})", "success")

            # Test getting workflow status
            response = requests.get(f"{base_url}/workflows/{thread_id}", timeout=5)
            if response.status_code == 200:
                print_status("Workflow status: OK", "success")
            else:
                print_status(f"Workflow status failed: {response.status_code}", "warning")

            # Test getting final post
            response = requests.get(f"{base_url}/workflows/{thread_id}/final", timeout=5)
            if response.status_code == 200:
                final_data = response.json()
                content_length = len(final_data.get("final_post", ""))
                print_status(f"Final post: OK ({content_length} chars)", "success")
            else:
                print_status(f"Final post failed: {response.status_code}", "warning")

        else:
            print_status(f"Workflow creation failed: {response.status_code}", "error")
            return False
    except Exception as e:
        print_status(f"Workflow creation error: {e}", "error")
        return False

    # Test documentation endpoints
    try:
        response = requests.get(f"{base_url}/docs", timeout=5)
        if response.status_code == 200:
            print_status("API documentation: OK", "success")
        else:
            print_status("API documentation: Not available", "warning")
    except:
        print_status("API documentation: Not available", "warning")

    return True


def start_streamlit_server() -> Optional[subprocess.Popen]:
    """Start the Streamlit server"""
    print_status("Starting Streamlit server...", "info")

    try:
        process = subprocess.Popen([
            sys.executable, "-m", "streamlit", "run", "apps/streamlit_app.py",
            "--server.port", "8501",
            "--server.headless", "true"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)

        # Wait for server to start
        max_attempts = 30
        for attempt in range(max_attempts):
            try:
                response = requests.get("http://127.0.0.1:8501", timeout=1)
                if response.status_code == 200:
                    print_status("Streamlit server started successfully", "success")
                    return process
            except:
                time.sleep(1)

        print_status("Streamlit server failed to start within 30 seconds", "error")
        process.terminate()
        return None

    except Exception as e:
        print_status(f"Failed to start Streamlit server: {e}", "error")
        return None


def test_streamlit_server():
    """Test Streamlit server"""
    print_status("Testing Streamlit server...", "info")

    try:
        response = requests.get("http://127.0.0.1:8501", timeout=5)
        if response.status_code == 200:
            if "Streamlit" in response.text:
                print_status("Streamlit server: OK", "success")
                return True
            else:
                print_status("Streamlit server: Unexpected response", "warning")
                return False
        else:
            print_status(f"Streamlit server failed: {response.status_code}", "error")
            return False
    except Exception as e:
        print_status(f"Streamlit server error: {e}", "error")
        return False


def main():
    """Main test function"""
    print_status("🧪 Blog Workflow Services Test", "info")
    print_status("=" * 50, "info")

    # Change to project directory
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)

    # Test imports
    if not test_imports():
        print_status("Import tests failed. Exiting.", "error")
        return 1

    print_status("-" * 30, "info")

    # Start and test API server
    api_process = start_api_server()
    if not api_process:
        print_status("Failed to start API server. Exiting.", "error")
        return 1

    try:
        if not test_api_endpoints():
            print_status("API tests failed", "error")
            return 1

        print_status("-" * 30, "info")

        # Start and test Streamlit server
        streamlit_process = start_streamlit_server()
        if not streamlit_process:
            print_status("Failed to start Streamlit server", "warning")
        else:
            try:
                if test_streamlit_server():
                    print_status("All tests passed! 🎉", "success")
                else:
                    print_status("Streamlit tests failed", "warning")
            finally:
                streamlit_process.terminate()
                streamlit_process.wait()

    finally:
        # Cleanup API server
        api_process.terminate()
        api_process.wait()

    print_status("=" * 50, "info")
    print_status("Test completed", "info")
    return 0


if __name__ == "__main__":
    sys.exit(main())
