import streamlit as st
import requests
import json
import os
import time
from datetime import datetime
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple

# Add parent directory to path for imports from src
parent_dir = str(Path(__file__).parent.parent.absolute())
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# API Configuration
API_BASE_URL = os.environ.get("API_BASE_URL", "http://localhost:8000")

# App configuration
st.set_page_config(
    page_title="Blog Workflow System",
    page_icon="📝",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Session state initialization
if "thread_id" not in st.session_state:
    st.session_state.thread_id = None
if "workflow_status" not in st.session_state:
    st.session_state.workflow_status = None
if "topic" not in st.session_state:
    st.session_state.topic = ""
if "content_preview" not in st.session_state:
    st.session_state.content_preview = None
if "seo_suggestions" not in st.session_state:
    st.session_state.seo_suggestions = None
if "final_post" not in st.session_state:
    st.session_state.final_post = None
if "error_message" not in st.session_state:
    st.session_state.error_message = None
if "workflows_list" not in st.session_state:
    st.session_state.workflows_list = []

# Helper functions
def make_api_request(endpoint: str, method: str = "GET", params: Dict = None, data: Dict = None) -> Tuple[bool, Dict]:
    """Make API request with error handling"""
    url = f"{API_BASE_URL}{endpoint}"

    try:
        if method == "GET":
            response = requests.get(url, params=params)
        elif method == "POST":
            response = requests.post(url, json=data)
        elif method == "DELETE":
            response = requests.delete(url)
        else:
            return False, {"error": f"Unsupported method: {method}"}

        response.raise_for_status()
        return True, response.json()
    except requests.exceptions.RequestException as e:
        error_msg = f"API request failed: {str(e)}"
        if hasattr(e, "response") and e.response is not None:
            try:
                error_data = e.response.json()
                if "detail" in error_data:
                    error_msg = error_data["detail"]
            except:
                error_msg = f"HTTP Error: {e.response.status_code} - {e.response.text}"
        return False, {"error": error_msg}

def create_workflow(topic: str) -> Tuple[bool, str]:
    """Create a new workflow"""
    success, response = make_api_request(
        "/workflows",
        method="POST",
        data={"topic": topic}
    )

    if success:
        st.session_state.thread_id = response.get("thread_id")
        st.session_state.workflow_status = response.get("status")
        st.session_state.topic = topic
        return True, "Workflow created successfully"
    else:
        return False, response.get("error", "Unknown error creating workflow")

def get_workflow_status() -> None:
    """Get current workflow status"""
    if not st.session_state.thread_id:
        return

    success, response = make_api_request(f"/workflows/{st.session_state.thread_id}")

    if success:
        st.session_state.workflow_status = response.get("status")
        st.session_state.topic = response.get("topic", st.session_state.topic)
        st.session_state.error_message = response.get("error_message")
    else:
        st.session_state.error_message = response.get("error")

def submit_review(decision: str, comments: str) -> Tuple[bool, str]:
    """Submit review decision"""
    if not st.session_state.thread_id:
        return False, "No active workflow"

    success, response = make_api_request(
        f"/workflows/{st.session_state.thread_id}/review",
        method="POST",
        data={"decision": decision, "comments": comments}
    )

    if success:
        return True, "Review submitted successfully"
    else:
        return False, response.get("error", "Unknown error submitting review")

def get_content_preview() -> None:
    """Get content preview for review"""
    if not st.session_state.thread_id:
        return

    success, response = make_api_request(
        f"/workflows/{st.session_state.thread_id}/preview"
    )

    if success:
        st.session_state.content_preview = response.get("content_preview")
    else:
        st.session_state.error_message = response.get("error")
        st.session_state.content_preview = None

def get_seo_suggestions() -> None:
    """Get SEO suggestions"""
    if not st.session_state.thread_id:
        return

    success, response = make_api_request(
        f"/workflows/{st.session_state.thread_id}/seo"
    )

    if success:
        st.session_state.seo_suggestions = response.get("seo_suggestions")
    else:
        st.session_state.seo_suggestions = None

def get_final_post() -> None:
    """Get final blog post"""
    if not st.session_state.thread_id:
        return

    success, response = make_api_request(
        f"/workflows/{st.session_state.thread_id}/final"
    )

    if success:
        st.session_state.final_post = response.get("final_post")
    else:
        st.session_state.final_post = None

def list_workflows() -> None:
    """Get list of all workflows"""
    success, response = make_api_request("/workflows")

    if success:
        st.session_state.workflows_list = response
    else:
        st.session_state.workflows_list = []
        st.session_state.error_message = response.get("error")

def reset_workflow() -> None:
    """Reset current workflow state"""
    st.session_state.thread_id = None
    st.session_state.workflow_status = None
    st.session_state.topic = ""
    st.session_state.content_preview = None
    st.session_state.seo_suggestions = None
    st.session_state.final_post = None
    st.session_state.error_message = None

def delete_workflow() -> Tuple[bool, str]:
    """Delete current workflow"""
    if not st.session_state.thread_id:
        return False, "No active workflow"

    success, response = make_api_request(
        f"/workflows/{st.session_state.thread_id}",
        method="DELETE"
    )

    if success:
        reset_workflow()
        return True, "Workflow deleted successfully"
    else:
        return False, response.get("error", "Unknown error deleting workflow")

def load_workflow(thread_id: str) -> None:
    """Load an existing workflow"""
    st.session_state.thread_id = thread_id
    get_workflow_status()

def check_status_and_update() -> None:
    """Check workflow status and update UI accordingly"""
    if not st.session_state.thread_id:
        return

    get_workflow_status()

    status = st.session_state.workflow_status

    if status == "awaiting_review":
        get_content_preview()
    elif status == "completed" or status == "finalizer":
        get_seo_suggestions()
        get_final_post()

# UI Components
def render_header():
    """Render page header"""
    col1, col2 = st.columns([1, 3])
    with col1:
        st.image("https://raw.githubusercontent.com/streamlit/streamlit/master/examples/data/logo.png", width=100)
    with col2:
        st.title("Blog Workflow System")
        st.markdown("Create, review, and optimize blog content with an intelligent workflow system")

def render_sidebar():
    """Render sidebar"""
    st.sidebar.header("Workflow Control")

    if st.session_state.thread_id:
        st.sidebar.success(f"Active Workflow: {st.session_state.thread_id}")

        st.sidebar.subheader("Current Status")
        st.sidebar.info(f"Status: {st.session_state.workflow_status}")
        st.sidebar.info(f"Topic: {st.session_state.topic}")

        if st.sidebar.button("Refresh Status", key="refresh_status"):
            check_status_and_update()

        if st.sidebar.button("Reset Workflow", key="reset"):
            if st.sidebar.checkbox("Confirm reset?", key="confirm_reset"):
                success, message = delete_workflow()
                if success:
                    st.sidebar.success(message)
                else:
                    st.sidebar.error(message)

    st.sidebar.markdown("---")
    st.sidebar.subheader("Existing Workflows")

    if st.sidebar.button("List Workflows", key="list_workflows"):
        list_workflows()

    for workflow in st.session_state.workflows_list:
        if st.sidebar.button(
            f"{workflow['topic']} ({workflow['status']})",
            key=f"load_{workflow['thread_id']}"
        ):
            load_workflow(workflow['thread_id'])

    st.sidebar.markdown("---")

    # API connection status
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=2)
        if response.status_code == 200:
            st.sidebar.success("API Connected")
        else:
            st.sidebar.error("API Error")
    except:
        st.sidebar.error("API Unreachable")

def render_new_workflow():
    """Render new workflow form"""
    st.header("Start New Blog Workflow")

    with st.form("new_workflow_form"):
        topic = st.text_input("Blog Topic", key="new_topic", value=st.session_state.topic)
        submitted = st.form_submit_button("Start Workflow")

        if submitted and topic:
            success, message = create_workflow(topic)
            if success:
                st.success(message)
                time.sleep(1)
                st.rerun()
            else:
                st.error(message)

def render_awaiting_review():
    """Render content for human review"""
    st.header("Content Review")

    if st.session_state.content_preview:
        st.markdown("### Content Preview")
        with st.expander("Show Content", expanded=True):
            st.markdown(st.session_state.content_preview)

        st.markdown("### Submit Your Review")
        with st.form("review_form"):
            decision = st.radio(
                "Decision",
                options=["aprovar", "revisar", "rejeitar"],
                format_func=lambda x: {
                    "aprovar": "Approve",
                    "revisar": "Request Revision",
                    "rejeitar": "Reject"
                }.get(x, x),
                index=0
            )

            comments = st.text_area(
                "Comments",
                placeholder="Enter review comments or revision instructions here..."
            )

            submitted = st.form_submit_button("Submit Review")

            if submitted:
                success, message = submit_review(decision, comments)
                if success:
                    st.success(message)
                    time.sleep(1)
                    st.rerun()
                else:
                    st.error(message)
    else:
        st.info("Content is not yet ready for review. Please refresh status.")

def render_completed_workflow():
    """Render completed workflow with SEO and final post"""
    st.header("Final Blog Post")

    tabs = st.tabs(["Final Post", "SEO Suggestions", "Export"])

    with tabs[0]:
        if st.session_state.final_post:
            st.markdown(st.session_state.final_post)
        else:
            st.info("Final post not available. The workflow may still be in progress.")

    with tabs[1]:
        if st.session_state.seo_suggestions:
            # Primary and secondary keywords
            st.subheader("Keywords")
            primary = st.session_state.seo_suggestions.get("primary_keyword", "N/A")
            st.write(f"**Primary Keyword:** {primary}")

            secondary = st.session_state.seo_suggestions.get("secondary_keywords", [])
            if secondary:
                st.write("**Secondary Keywords:**")
                for kw in secondary:
                    st.write(f"- {kw}")

            # Meta description
            st.subheader("Meta Description")
            meta = st.session_state.seo_suggestions.get("meta_description", "N/A")
            st.write(meta)

            # Title suggestions
            titles = st.session_state.seo_suggestions.get("title_suggestions", [])
            if titles:
                st.subheader("Title Suggestions")
                for i, title in enumerate(titles, 1):
                    st.write(f"{i}. {title}")

            # Content improvements
            improvements = st.session_state.seo_suggestions.get("content_improvements", [])
            if improvements:
                st.subheader("Improvement Suggestions")
                for imp in improvements:
                    st.write(f"- {imp}")

            # Technical SEO
            tech_seo = st.session_state.seo_suggestions.get("technical_seo", {})
            if tech_seo:
                st.subheader("Technical SEO")
                for key, value in tech_seo.items():
                    formatted_key = key.replace("_", " ").title()
                    if isinstance(value, list):
                        st.write(f"**{formatted_key}:**")
                        for item in value:
                            st.write(f"- {item}")
                    else:
                        st.write(f"**{formatted_key}:** {value}")
        else:
            st.info("SEO suggestions not available.")

    with tabs[2]:
        st.subheader("Export Options")

        if st.session_state.final_post:
            col1, col2 = st.columns(2)

            with col1:
                if st.button("Copy to Clipboard", key="copy_clipboard"):
                    # This doesn't work in Streamlit's server-side model
                    # but shows the intent. In a real app, we'd use JavaScript
                    st.code(st.session_state.final_post)
                    st.success("Text copied to clipboard! (Please manually select and copy)")

            with col2:
                filename = f"blog_{st.session_state.thread_id}.md"
                st.download_button(
                    "Download as Markdown",
                    st.session_state.final_post,
                    file_name=filename,
                    mime="text/markdown"
                )

            # Export with metadata
            metadata = {
                "thread_id": st.session_state.thread_id,
                "topic": st.session_state.topic,
                "creation_date": datetime.now().isoformat(),
                "seo_data": st.session_state.seo_suggestions
            }

            export_json = json.dumps({
                "metadata": metadata,
                "content": st.session_state.final_post
            }, indent=2)

            st.download_button(
                "Export with Metadata (JSON)",
                export_json,
                file_name=f"blog_{st.session_state.thread_id}_full.json",
                mime="application/json"
            )
        else:
            st.info("No content available to export.")

def render_error_state():
    """Render error state"""
    st.error(f"Error: {st.session_state.error_message}")

    if st.button("Reset Workflow"):
        reset_workflow()

def render_main_content():
    """Render main content based on workflow state"""
    if st.session_state.error_message:
        render_error_state()
        return

    if not st.session_state.thread_id:
        render_new_workflow()
        return

    status = st.session_state.workflow_status

    if status == "awaiting_review":
        render_awaiting_review()
    elif status in ["completed", "finalizer"]:
        render_completed_workflow()
    elif status in ["not_started", "initiated", "topic_researcher", "outline_generator", "content_writer"]:
        st.info("Workflow is in progress. Please wait and refresh status.")

        progress_messages = {
            "not_started": "Preparing to start workflow...",
            "initiated": "Workflow initiated. Setting up...",
            "topic_researcher": "Researching topic...",
            "outline_generator": "Generating outline...",
            "content_writer": "Writing content..."
        }

        message = progress_messages.get(status, f"Processing: {status}")

        st.write(message)
        st.progress(0.5)  # Show indeterminate progress

        if st.button("Refresh Status"):
            check_status_and_update()
    else:
        st.warning(f"Unknown workflow status: {status}")
        if st.button("Refresh Status"):
            check_status_and_update()

# Main app
def main():
    render_header()
    render_sidebar()

    # Check if API is available
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=2)
        if response.status_code != 200:
            st.error("API service is not responding correctly. Please check the API status.")
            return
    except:
        st.error("Cannot connect to API service. Please make sure it's running.")
        st.code(f"API URL: {API_BASE_URL}")
        return

    render_main_content()

if __name__ == "__main__":
    main()
