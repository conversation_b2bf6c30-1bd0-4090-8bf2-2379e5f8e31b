import logging
import os
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
from pathlib import Path

from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends, Query, Body
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

# Add parent directory to path for imports from src
import sys
parent_dir = str(Path(__file__).parent.parent.absolute())
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from src.blog_workflow import (
    BlogWorkflowManager,
    WorkflowConfig,
    WorkflowStatus,
    create_persistent_workflow_manager,
    WorkflowError,
    MaxRevisionsExceededError,
    WorkflowExecutionError
)
from src.models.workflow_state import ReviewStatus, ReviewFeedback

# Configure logging
logging.basicConfig(
    level=os.environ.get("LOG_LEVEL", "INFO"),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("api")

# Set up the database path
DB_PATH = os.environ.get("DATABASE_PATH", "./data/workflow.db")
os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)

# Initialize app
app = FastAPI(
    title="Blog Workflow API",
    description="API for Blog Content Creation Workflow with LangGraph",
    version="2.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, restrict this to your frontend domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for request/response
class WorkflowRequest(BaseModel):
    topic: Optional[str] = Field(None, description="Topic for new workflow")
    thread_id: Optional[str] = Field(None, description="Optional thread ID")

class ReviewRequest(BaseModel):
    decision: str = Field(..., description="Review decision: 'aprovar', 'revisar', or 'rejeitar'")
    comments: Optional[str] = Field(None, description="Review comments")

class WorkflowResponse(BaseModel):
    thread_id: str
    status: str
    topic: Optional[str] = None
    revision_count: int = 0
    human_approved: bool = False
    error_message: Optional[str] = None

class ContentPreviewResponse(BaseModel):
    thread_id: str
    content_preview: str
    revision_count: int
    topic: str
    content_length: Optional[int] = None
    estimated_reading_time: Optional[int] = None

class SEOResponse(BaseModel):
    thread_id: str
    seo_suggestions: Dict[str, Any]

class FinalPostResponse(BaseModel):
    thread_id: str
    final_post: str
    word_count: Optional[int] = None
    estimated_reading_time: Optional[int] = None

class HealthResponse(BaseModel):
    status: str
    timestamp: str
    version: str

class ErrorResponse(BaseModel):
    error: str
    details: Optional[str] = None

# Global workflow manager
workflow_manager = None

def get_workflow_manager() -> BlogWorkflowManager:
    """Get the global workflow manager instance"""
    global workflow_manager
    if workflow_manager is None:
        workflow_manager = create_persistent_workflow_manager(DB_PATH)
        logger.info(f"Workflow manager initialized with database: {DB_PATH}")
    return workflow_manager

# Exception handlers
@app.exception_handler(WorkflowError)
async def workflow_error_handler(request, exc):
    return JSONResponse(
        status_code=400,
        content={"error": "Workflow Error", "details": str(exc)}
    )

@app.exception_handler(MaxRevisionsExceededError)
async def max_revisions_error_handler(request, exc):
    return JSONResponse(
        status_code=400,
        content={"error": "Maximum Revisions Exceeded", "details": str(exc)}
    )

# Health endpoint
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now().isoformat(),
        version="2.0.0"
    )

# Workflow endpoints
@app.post("/workflows", response_model=WorkflowResponse)
async def create_workflow(request: WorkflowRequest):
    """Create a new blog workflow"""
    try:
        if not request.topic:
            raise HTTPException(status_code=400, detail="Topic is required")

        manager = get_workflow_manager()

        # Generate thread_id if not provided
        thread_id = request.thread_id or f"workflow_{int(datetime.now().timestamp())}"

        # Run workflow in background
        result = manager.run_workflow(thread_id, topic=request.topic)

        status = manager.get_workflow_status(thread_id)

        return WorkflowResponse(
            thread_id=thread_id,
            status=status.current_step,
            topic=request.topic,
            revision_count=status.revision_count,
            human_approved=status.human_approved,
            error_message=status.error_message
        )

    except Exception as e:
        logger.error(f"Error creating workflow: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/workflows", response_model=List[WorkflowResponse])
async def list_workflows():
    """List all workflows"""
    try:
        manager = get_workflow_manager()
        workflows = manager.list_workflows()

        response = []
        for workflow_info in workflows:
            thread_id = workflow_info["thread_id"]
            status = manager.get_workflow_status(thread_id)

            response.append(WorkflowResponse(
                thread_id=thread_id,
                status=status.current_step,
                topic=status.topic,
                revision_count=status.revision_count,
                human_approved=status.human_approved,
                error_message=status.error_message
            ))

        return response

    except Exception as e:
        logger.error(f"Error listing workflows: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/workflows/{thread_id}", response_model=WorkflowResponse)
async def get_workflow_status(thread_id: str):
    """Get workflow status"""
    try:
        manager = get_workflow_manager()
        status = manager.get_workflow_status(thread_id)

        return WorkflowResponse(
            thread_id=thread_id,
            status=status.current_step,
            topic=status.topic,
            revision_count=status.revision_count,
            human_approved=status.human_approved,
            error_message=status.error_message
        )

    except Exception as e:
        logger.error(f"Error getting workflow status: {e}")
        raise HTTPException(status_code=404, detail=f"Workflow {thread_id} not found")

@app.post("/workflows/{thread_id}/review", response_model=WorkflowResponse)
async def submit_review(thread_id: str, request: ReviewRequest):
    """Submit human review for workflow"""
    try:
        manager = get_workflow_manager()

        # Map decision strings to ReviewStatus
        decision_map = {
            "aprovar": ReviewStatus.APPROVED,
            "revisar": ReviewStatus.REVISION_REQUESTED,
            "rejeitar": ReviewStatus.REJECTED
        }

        status = decision_map.get(request.decision.lower())
        if not status:
            raise HTTPException(status_code=400, detail="Invalid decision. Use 'aprovar', 'revisar', or 'rejeitar'")

        review_feedback = ReviewFeedback(
            status=status,
            comments=request.comments or ""
        )

        result = manager.submit_human_review(thread_id, review_feedback)
        workflow_status = manager.get_workflow_status(thread_id)

        return WorkflowResponse(
            thread_id=thread_id,
            status=workflow_status.current_step,
            topic=workflow_status.topic,
            revision_count=workflow_status.revision_count,
            human_approved=workflow_status.human_approved,
            error_message=workflow_status.error_message
        )

    except Exception as e:
        logger.error(f"Error submitting review: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/workflows/{thread_id}/preview", response_model=ContentPreviewResponse)
async def get_content_preview(thread_id: str):
    """Get content preview for review"""
    try:
        manager = get_workflow_manager()
        status = manager.get_workflow_status(thread_id)

        if status.current_step != "awaiting_review":
            raise HTTPException(status_code=400, detail="Workflow is not awaiting review")

        # Get the draft content from workflow state
        state = manager.get_workflow_state(thread_id)
        draft_content = state.get("draft_content", "")

        if not draft_content:
            raise HTTPException(status_code=404, detail="No content available for preview")

        # Limit preview length
        preview_length = 2000  # Configurable
        content_preview = draft_content[:preview_length]
        if len(draft_content) > preview_length:
            content_preview += "\n\n... (content truncated for preview)"

        return ContentPreviewResponse(
            thread_id=thread_id,
            content_preview=content_preview,
            revision_count=status.revision_count,
            topic=status.topic,
            content_length=len(draft_content),
            estimated_reading_time=state.get("estimated_reading_time")
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting content preview: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/workflows/{thread_id}/seo", response_model=SEOResponse)
async def get_seo_suggestions(thread_id: str):
    """Get SEO suggestions for workflow"""
    try:
        manager = get_workflow_manager()
        state = manager.get_workflow_state(thread_id)

        seo_suggestions = state.get("seo_suggestions", {})

        return SEOResponse(
            thread_id=thread_id,
            seo_suggestions=seo_suggestions
        )

    except Exception as e:
        logger.error(f"Error getting SEO suggestions: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/workflows/{thread_id}/final", response_model=FinalPostResponse)
async def get_final_post(thread_id: str):
    """Get final blog post"""
    try:
        manager = get_workflow_manager()
        state = manager.get_workflow_state(thread_id)

        final_post_data = state.get("final_post", "")
        if not final_post_data:
            raise HTTPException(status_code=404, detail="Final post not available")

        # Handle both string and dict formats
        if isinstance(final_post_data, dict):
            # Extract content from structured format
            final_post_content = final_post_data.get("content", "")
            word_count = final_post_data.get("metadata", {}).get("word_count")
            estimated_reading_time = final_post_data.get("metadata", {}).get("estimated_reading_time")
        else:
            # Handle string format
            final_post_content = str(final_post_data)
            word_count = state.get("final_word_count")
            estimated_reading_time = state.get("estimated_reading_time")

        if not final_post_content:
            raise HTTPException(status_code=404, detail="Final post content not available")

        return FinalPostResponse(
            thread_id=thread_id,
            final_post=final_post_content,
            word_count=word_count,
            estimated_reading_time=estimated_reading_time
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting final post: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/workflows/{thread_id}")
async def delete_workflow(thread_id: str):
    """Delete a workflow"""
    try:
        manager = get_workflow_manager()
        manager.delete_workflow(thread_id)

        return {"message": f"Workflow {thread_id} deleted successfully"}

    except Exception as e:
        logger.error(f"Error deleting workflow: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize application on startup"""
    logger.info("Starting Blog Workflow API")

    # Initialize workflow manager
    manager = get_workflow_manager()
    logger.info("Application startup complete")

if __name__ == "__main__":
    import uvicorn

    port = int(os.environ.get("PORT", 8000))
    host = os.environ.get("HOST", "0.0.0.0")

    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=os.environ.get("RELOAD", "false").lower() == "true"
    )
