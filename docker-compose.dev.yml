# ===================================================================
# Blog Workflow LangGraph - Development Docker Compose Override
# ===================================================================
# Use with: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

version: "3.8"

services:
  blog-workflow-api:
    build:
      context: .
      dockerfile: Dockerfile.api
      target: builder  # Use builder stage for development
    volumes:
      - .:/app
      - /app/venv  # Exclude venv from volume mount
    environment:
      - DEBUG=true
      - DEVELOPMENT_MODE=true
      - LOG_LEVEL=DEBUG
      - API_RELOAD=true
    command: ["uvicorn", "api.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
    ports:
      - "8000:8000"
      - "5678:5678"  # Debug port

  blog-workflow-streamlit:
    build:
      context: .
      dockerfile: Dockerfile.streamlit
      target: builder  # Use builder stage for development
    volumes:
      - .:/app
      - /app/venv  # Exclude venv from volume mount
    environment:
      - DEBUG=true
      - DEVELOPMENT_MODE=true
      - STREAMLIT_SERVER_RUNON_SAVE=true
      - STREAMLIT_SERVER_FILE_WATCHER_TYPE=poll
    command: ["streamlit", "run", "apps/streamlit_app.py", "--server.address", "0.0.0.0", "--server.port", "8501", "--server.fileWatcherType", "poll"]
    ports:
      - "8501:8501"

  # Development database (optional)
  postgres-dev:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: blog_workflow_dev
      POSTGRES_USER: dev_user
      POSTGRES_PASSWORD: dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    profiles:
      - database

  # Redis for caching (optional)
  redis-dev:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    profiles:
      - cache

  # Development tools container
  dev-tools:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - .:/app
    environment:
      - PYTHONPATH=/app
    command: ["sleep", "infinity"]
    profiles:
      - tools

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  default:
    name: blog-workflow-dev-network
